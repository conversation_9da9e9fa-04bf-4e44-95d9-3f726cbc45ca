<?php

namespace App\Models;

use Core\Application;

/**
 * User Model
 * Otel Yönetim Sistemi
 */
class User
{
    private $db;
    private $table = 'users';
    
    public function __construct()
    {
        $this->db = Application::getInstance()->getDatabase();
    }
    
    /**
     * Find user by ID
     */
    public function find($id)
    {
        return $this->db->selectOne(
            "SELECT * FROM {$this->table} WHERE id = :id",
            ['id' => $id]
        );
    }
    
    /**
     * Find user by email
     */
    public function findByEmail($email)
    {
        return $this->db->selectOne(
            "SELECT * FROM {$this->table} WHERE email = :email",
            ['email' => $email]
        );
    }
    
    /**
     * Get all users with pagination
     */
    public function paginate($page = 1, $perPage = 20, $search = '')
    {
        $offset = ($page - 1) * $perPage;
        
        $whereClause = '';
        $params = [];
        
        if (!empty($search)) {
            $whereClause = "WHERE name LIKE :search OR email LIKE :search";
            $params['search'] = "%{$search}%";
        }
        
        // Get total count
        $totalQuery = "SELECT COUNT(*) as total FROM {$this->table} {$whereClause}";
        $total = $this->db->selectOne($totalQuery, $params)['total'];
        
        // Get users
        $query = "SELECT id, name, email, role, status, created_at, last_login 
                  FROM {$this->table} 
                  {$whereClause} 
                  ORDER BY created_at DESC 
                  LIMIT {$perPage} OFFSET {$offset}";
        
        $users = $this->db->select($query, $params);
        
        return [
            'data' => $users,
            'total' => $total,
            'per_page' => $perPage,
            'current_page' => $page,
            'last_page' => ceil($total / $perPage),
            'from' => $offset + 1,
            'to' => min($offset + $perPage, $total)
        ];
    }
    
    /**
     * Create new user
     */
    public function create($data)
    {
        // Hash password
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->insert($this->table, $data);
    }
    
    /**
     * Update user
     */
    public function update($id, $data)
    {
        // Hash password if provided
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        } else {
            unset($data['password']);
        }
        
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->update($this->table, $data, 'id = :id', ['id' => $id]);
    }
    
    /**
     * Delete user
     */
    public function delete($id)
    {
        return $this->db->delete($this->table, 'id = :id', ['id' => $id]);
    }
    
    /**
     * Check if email exists
     */
    public function emailExists($email, $excludeId = null)
    {
        $query = "SELECT COUNT(*) as count FROM {$this->table} WHERE email = :email";
        $params = ['email' => $email];
        
        if ($excludeId) {
            $query .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        $result = $this->db->selectOne($query, $params);
        return $result['count'] > 0;
    }
    
    /**
     * Get users by role
     */
    public function getByRole($role)
    {
        return $this->db->select(
            "SELECT * FROM {$this->table} WHERE role = :role AND status = 'active' ORDER BY name",
            ['role' => $role]
        );
    }
    
    /**
     * Update last login
     */
    public function updateLastLogin($id, $ip = null)
    {
        return $this->db->update($this->table, [
            'last_login' => date('Y-m-d H:i:s'),
            'last_login_ip' => $ip
        ], 'id = :id', ['id' => $id]);
    }
    
    /**
     * Change user status
     */
    public function changeStatus($id, $status)
    {
        return $this->db->update($this->table, [
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s')
        ], 'id = :id', ['id' => $id]);
    }
    
    /**
     * Get user statistics
     */
    public function getStats()
    {
        $stats = [];
        
        // Total users
        $stats['total'] = $this->db->selectOne("SELECT COUNT(*) as count FROM {$this->table}")['count'];
        
        // Active users
        $stats['active'] = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM {$this->table} WHERE status = 'active'"
        )['count'];
        
        // Users by role
        $roleStats = $this->db->select(
            "SELECT role, COUNT(*) as count FROM {$this->table} GROUP BY role"
        );
        
        foreach ($roleStats as $role) {
            $stats['roles'][$role['role']] = $role['count'];
        }
        
        // Recent registrations (last 30 days)
        $stats['recent'] = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM {$this->table} 
             WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)"
        )['count'];
        
        return $stats;
    }
    
    /**
     * Search users
     */
    public function search($query, $limit = 10)
    {
        return $this->db->select(
            "SELECT id, name, email, role FROM {$this->table} 
             WHERE (name LIKE :query OR email LIKE :query) 
             AND status = 'active' 
             ORDER BY name 
             LIMIT {$limit}",
            ['query' => "%{$query}%"]
        );
    }
    
    /**
     * Get user permissions based on role
     */
    public function getPermissions($userId)
    {
        $user = $this->find($userId);
        if (!$user) {
            return [];
        }
        
        $rolePermissions = [
            'super_admin' => ['*'],
            'admin' => [
                'dashboard.view', 'users.manage', 'hotels.manage', 
                'reservations.manage', 'content.manage', 'media.manage',
                'system.settings', 'reports.view', 'marketing.manage'
            ],
            'manager' => [
                'dashboard.view', 'hotels.manage', 'reservations.manage',
                'content.view', 'reports.view'
            ],
            'staff' => [
                'dashboard.view', 'reservations.view', 'content.view'
            ],
        ];
        
        return $rolePermissions[$user['role']] ?? [];
    }
    
    /**
     * Validate user data
     */
    public function validate($data, $isUpdate = false, $userId = null)
    {
        $errors = [];
        
        // Name validation
        if (empty($data['name'])) {
            $errors['name'][] = 'Ad alanı zorunludur.';
        } elseif (strlen($data['name']) < 2) {
            $errors['name'][] = 'Ad en az 2 karakter olmalıdır.';
        }
        
        // Email validation
        if (empty($data['email'])) {
            $errors['email'][] = 'Email alanı zorunludur.';
        } elseif (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'][] = 'Geçerli bir email adresi giriniz.';
        } elseif ($this->emailExists($data['email'], $userId)) {
            $errors['email'][] = 'Bu email adresi zaten kullanılmaktadır.';
        }
        
        // Password validation (only for new users or when password is provided)
        if (!$isUpdate || !empty($data['password'])) {
            if (empty($data['password'])) {
                $errors['password'][] = 'Şifre alanı zorunludur.';
            } elseif (strlen($data['password']) < 8) {
                $errors['password'][] = 'Şifre en az 8 karakter olmalıdır.';
            }
            
            if (isset($data['password_confirmation']) && $data['password'] !== $data['password_confirmation']) {
                $errors['password_confirmation'][] = 'Şifreler eşleşmiyor.';
            }
        }
        
        // Role validation
        $validRoles = ['super_admin', 'admin', 'manager', 'staff'];
        if (empty($data['role'])) {
            $errors['role'][] = 'Rol alanı zorunludur.';
        } elseif (!in_array($data['role'], $validRoles)) {
            $errors['role'][] = 'Geçersiz rol seçimi.';
        }
        
        // Status validation
        $validStatuses = ['active', 'inactive'];
        if (isset($data['status']) && !in_array($data['status'], $validStatuses)) {
            $errors['status'][] = 'Geçersiz durum seçimi.';
        }
        
        return $errors;
    }
}
