<?php
/**
 * Admin Panel Entry Point
 * Otel Yönetim <PERSON> - Admin Panel
 */

// Define constants
define('ADMIN_ROOT', dirname(__DIR__));
define('PUBLIC_PATH', __DIR__);
define('STORAGE_PATH', ADMIN_ROOT . '/storage');
define('CONFIG_PATH', ADMIN_ROOT . '/config');
define('APP_PATH', ADMIN_ROOT . '/app');
define('CORE_PATH', ADMIN_ROOT . '/core');

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include autoloader
require_once CORE_PATH . '/Autoloader.php';

// Register autoloader
Autoloader::register();

try {
    // Initialize and run application
    $app = Core\Application::getInstance();
    $app->run();
    
} catch (Exception $e) {
    // Log error
    error_log("Fatal error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    
    // Show error page
    http_response_code(500);
    
    if (isset($_ENV['APP_DEBUG']) && $_ENV['APP_DEBUG']) {
        echo "<h1>Application Error</h1>";
        echo "<p><strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
        echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
        echo "<h3>Stack Trace:</h3>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    } else {
        echo "<h1>Bir hata oluştu</h1>";
        echo "<p>Lütfen daha sonra tekrar deneyin.</p>";
    }
}
