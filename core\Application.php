<?php

namespace Core;

/**
 * Main Application Class
 * Otel Yönetim <PERSON>
 */
class Application
{
    private static $instance = null;
    private $config = [];
    private $router;
    private $database;
    private $session;
    
    private function __construct()
    {
        $this->loadConfiguration();
        $this->initializeServices();
    }
    
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function loadConfiguration()
    {
        // Load environment variables
        if (file_exists(__DIR__ . '/../.env')) {
            $this->loadEnvFile(__DIR__ . '/../.env');
        }
        
        // Load configuration files
        $this->config = [
            'app' => require __DIR__ . '/../config/app.php',
            'database' => require __DIR__ . '/../config/database.php',
        ];
    }
    
    private function loadEnvFile($path)
    {
        if (!file_exists($path)) {
            return;
        }
        
        $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos(trim($line), '#') === 0) {
                continue;
            }
            
            list($name, $value) = explode('=', $line, 2);
            $name = trim($name);
            $value = trim($value);
            
            if (!array_key_exists($name, $_ENV)) {
                $_ENV[$name] = $value;
            }
        }
    }
    
    private function initializeServices()
    {
        // Initialize database
        $this->database = new Database($this->config['database']);
        
        // Initialize session
        $this->session = new Session($this->config['app']['session']);
        
        // Initialize router
        $this->router = new Router();
        
        // Set timezone
        date_default_timezone_set($this->config['app']['timezone']);
        
        // Set locale
        setlocale(LC_ALL, $this->config['app']['locale']);
    }
    
    public function run()
    {
        try {
            // Start session
            $this->session->start();
            
            // Handle CSRF protection for POST requests
            $this->handleCSRF();
            
            // Route the request
            $this->router->dispatch();
            
        } catch (\Exception $e) {
            $this->handleException($e);
        }
    }
    
    private function handleCSRF()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $token = $_POST['_token'] ?? $_SERVER['HTTP_X_CSRF_TOKEN'] ?? null;
            if (!$this->session->validateCSRFToken($token)) {
                throw new \Exception('CSRF token mismatch', 419);
            }
        }
    }
    
    private function handleException(\Exception $e)
    {
        $logger = new Logger();
        $logger->error($e->getMessage(), [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]);
        
        if ($this->config['app']['debug']) {
            echo "<h1>Error: " . $e->getMessage() . "</h1>";
            echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
            echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
            echo "<h3>Stack Trace:</h3>";
            echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
        } else {
            http_response_code(500);
            $this->renderErrorPage(500);
        }
    }
    
    private function renderErrorPage($code)
    {
        $errorFile = __DIR__ . "/../app/views/errors/{$code}.php";
        if (file_exists($errorFile)) {
            include $errorFile;
        } else {
            echo "<h1>Bir hata oluştu</h1>";
            echo "<p>Lütfen daha sonra tekrar deneyin.</p>";
        }
    }
    
    public function getConfig($key = null)
    {
        if ($key === null) {
            return $this->config;
        }
        
        $keys = explode('.', $key);
        $value = $this->config;
        
        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return null;
            }
            $value = $value[$k];
        }
        
        return $value;
    }
    
    public function getDatabase()
    {
        return $this->database;
    }
    
    public function getSession()
    {
        return $this->session;
    }
    
    public function getRouter()
    {
        return $this->router;
    }
    
    public function isAdmin()
    {
        $uri = $_SERVER['REQUEST_URI'];
        return strpos($uri, '/admin') === 0;
    }
    
    public function isFrontend()
    {
        return !$this->isAdmin();
    }
}
