/**
 * Admin Panel JavaScript
 * Otel Yönetim Sistemi
 */

// Global admin object
window.Admin = {
    // Configuration
    config: {
        csrfToken: window.csrfToken || '',
        apiUrl: '/admin/api',
        locale: 'tr-TR',
        currency: 'TRY'
    },

    // Initialize admin panel
    init: function() {
        this.setupEventListeners();
        this.initializeComponents();
        this.setupAjaxDefaults();
        this.initializeTooltips();
        this.setupKeyboardShortcuts();
    },

    // Setup global event listeners
    setupEventListeners: function() {
        // Confirm delete buttons
        document.addEventListener('click', function(e) {
            if (e.target.matches('.confirm-delete') || e.target.closest('.confirm-delete')) {
                e.preventDefault();
                const button = e.target.matches('.confirm-delete') ? e.target : e.target.closest('.confirm-delete');
                const message = button.getAttribute('data-message') || '<PERSON>u işlemi geri alamazsınız. Emin misiniz?';
                
                if (confirm(message)) {
                    if (button.tagName === 'A') {
                        window.location.href = button.href;
                    } else if (button.tagName === 'BUTTON' && button.form) {
                        button.form.submit();
                    }
                }
            }
        });

        // Auto-submit forms on change
        document.addEventListener('change', function(e) {
            if (e.target.matches('.auto-submit')) {
                e.target.form.submit();
            }
        });

        // Toggle password visibility
        document.addEventListener('click', function(e) {
            if (e.target.matches('.toggle-password')) {
                const input = document.querySelector(e.target.getAttribute('data-target'));
                const icon = e.target.querySelector('i');
                
                if (input.type === 'password') {
                    input.type = 'text';
                    icon.className = 'fas fa-eye-slash';
                } else {
                    input.type = 'password';
                    icon.className = 'fas fa-eye';
                }
            }
        });

        // File upload preview
        document.addEventListener('change', function(e) {
            if (e.target.matches('input[type="file"]') && e.target.files[0]) {
                const file = e.target.files[0];
                const preview = document.querySelector(e.target.getAttribute('data-preview'));
                
                if (preview && file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        preview.src = e.target.result;
                        preview.style.display = 'block';
                    };
                    reader.readAsDataURL(file);
                }
            }
        });
    },

    // Initialize components
    initializeComponents: function() {
        // Initialize dropdowns
        this.initDropdowns();
        
        // Initialize modals
        this.initModals();
        
        // Initialize data tables
        this.initDataTables();
        
        // Initialize date pickers
        this.initDatePickers();
        
        // Initialize rich text editors
        this.initRichTextEditors();
    },

    // Setup AJAX defaults
    setupAjaxDefaults: function() {
        // Add CSRF token to all AJAX requests
        const originalFetch = window.fetch;
        window.fetch = function(url, options = {}) {
            if (options.method && options.method.toUpperCase() !== 'GET') {
                options.headers = options.headers || {};
                options.headers['X-CSRF-TOKEN'] = Admin.config.csrfToken;
            }
            return originalFetch(url, options);
        };
    },

    // Initialize tooltips
    initializeTooltips: function() {
        const tooltips = document.querySelectorAll('[data-tooltip]');
        tooltips.forEach(element => {
            element.addEventListener('mouseenter', function() {
                const tooltip = document.createElement('div');
                tooltip.className = 'tooltip';
                tooltip.textContent = this.getAttribute('data-tooltip');
                
                const rect = this.getBoundingClientRect();
                tooltip.style.top = (rect.top - 30) + 'px';
                tooltip.style.left = rect.left + 'px';
                
                document.body.appendChild(tooltip);
                
                this.addEventListener('mouseleave', function() {
                    tooltip.remove();
                }, { once: true });
            });
        });
    },

    // Setup keyboard shortcuts
    setupKeyboardShortcuts: function() {
        document.addEventListener('keydown', function(e) {
            // Ctrl + S for save
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                const saveBtn = document.querySelector('button[type="submit"], .save-btn');
                if (saveBtn) saveBtn.click();
            }
            
            // Ctrl + N for new
            if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                const newBtn = document.querySelector('.new-btn, [href*="create"]');
                if (newBtn) newBtn.click();
            }
            
            // Escape to close modals
            if (e.key === 'Escape') {
                Admin.closeAllModals();
            }
        });
    },

    // Initialize dropdowns
    initDropdowns: function() {
        const dropdowns = document.querySelectorAll('.dropdown');
        dropdowns.forEach(dropdown => {
            const button = dropdown.querySelector('.dropdown-button');
            const menu = dropdown.querySelector('.dropdown-menu');
            
            if (button && menu) {
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    menu.classList.toggle('hidden');
                });
                
                document.addEventListener('click', function() {
                    menu.classList.add('hidden');
                });
            }
        });
    },

    // Initialize modals
    initModals: function() {
        const modalTriggers = document.querySelectorAll('[data-modal]');
        modalTriggers.forEach(trigger => {
            trigger.addEventListener('click', function(e) {
                e.preventDefault();
                const modalId = this.getAttribute('data-modal');
                Admin.openModal(modalId);
            });
        });

        const modalCloses = document.querySelectorAll('.modal-close');
        modalCloses.forEach(close => {
            close.addEventListener('click', function() {
                Admin.closeModal(this.closest('.modal'));
            });
        });
    },

    // Initialize data tables
    initDataTables: function() {
        const tables = document.querySelectorAll('.data-table');
        tables.forEach(table => {
            // Add sorting functionality
            const headers = table.querySelectorAll('th[data-sort]');
            headers.forEach(header => {
                header.style.cursor = 'pointer';
                header.addEventListener('click', function() {
                    const column = this.getAttribute('data-sort');
                    Admin.sortTable(table, column);
                });
            });
        });
    },

    // Initialize date pickers
    initDatePickers: function() {
        const datePickers = document.querySelectorAll('input[type="date"]');
        datePickers.forEach(picker => {
            // Add date validation and formatting
            picker.addEventListener('change', function() {
                if (this.value) {
                    const date = new Date(this.value);
                    if (isNaN(date.getTime())) {
                        this.setCustomValidity('Geçerli bir tarih giriniz');
                    } else {
                        this.setCustomValidity('');
                    }
                }
            });
        });
    },

    // Initialize rich text editors
    initRichTextEditors: function() {
        const editors = document.querySelectorAll('.rich-editor');
        editors.forEach(editor => {
            // Simple rich text editor implementation
            // In production, you might want to use a library like TinyMCE or CKEditor
            this.createSimpleEditor(editor);
        });
    },

    // Create simple rich text editor
    createSimpleEditor: function(textarea) {
        const toolbar = document.createElement('div');
        toolbar.className = 'editor-toolbar flex space-x-2 mb-2 p-2 border border-gray-300 rounded-t-md bg-gray-50';
        
        const buttons = [
            { icon: 'fas fa-bold', command: 'bold', title: 'Kalın' },
            { icon: 'fas fa-italic', command: 'italic', title: 'İtalik' },
            { icon: 'fas fa-underline', command: 'underline', title: 'Altı Çizili' },
            { icon: 'fas fa-list-ul', command: 'insertUnorderedList', title: 'Madde İşareti' },
            { icon: 'fas fa-list-ol', command: 'insertOrderedList', title: 'Numaralı Liste' },
            { icon: 'fas fa-link', command: 'createLink', title: 'Bağlantı' }
        ];
        
        buttons.forEach(btn => {
            const button = document.createElement('button');
            button.type = 'button';
            button.className = 'p-1 hover:bg-gray-200 rounded';
            button.innerHTML = `<i class="${btn.icon}"></i>`;
            button.title = btn.title;
            
            button.addEventListener('click', function() {
                if (btn.command === 'createLink') {
                    const url = prompt('Bağlantı URL\'si:');
                    if (url) {
                        document.execCommand(btn.command, false, url);
                    }
                } else {
                    document.execCommand(btn.command, false, null);
                }
            });
            
            toolbar.appendChild(button);
        });
        
        const editorDiv = document.createElement('div');
        editorDiv.contentEditable = true;
        editorDiv.className = 'min-h-32 p-3 border border-gray-300 rounded-b-md focus:outline-none focus:ring-2 focus:ring-blue-500';
        editorDiv.innerHTML = textarea.value;
        
        editorDiv.addEventListener('input', function() {
            textarea.value = this.innerHTML;
        });
        
        textarea.style.display = 'none';
        textarea.parentNode.insertBefore(toolbar, textarea);
        textarea.parentNode.insertBefore(editorDiv, textarea);
    },

    // Utility functions
    openModal: function(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('hidden');
            modal.classList.add('flex');
            document.body.style.overflow = 'hidden';
        }
    },

    closeModal: function(modal) {
        if (modal) {
            modal.classList.add('hidden');
            modal.classList.remove('flex');
            document.body.style.overflow = '';
        }
    },

    closeAllModals: function() {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => this.closeModal(modal));
    },

    sortTable: function(table, column) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const columnIndex = Array.from(table.querySelectorAll('th')).findIndex(th => th.getAttribute('data-sort') === column);
        
        if (columnIndex === -1) return;
        
        const isAscending = table.getAttribute('data-sort-direction') !== 'asc';
        table.setAttribute('data-sort-direction', isAscending ? 'asc' : 'desc');
        
        rows.sort((a, b) => {
            const aValue = a.cells[columnIndex].textContent.trim();
            const bValue = b.cells[columnIndex].textContent.trim();
            
            if (isAscending) {
                return aValue.localeCompare(bValue, 'tr', { numeric: true });
            } else {
                return bValue.localeCompare(aValue, 'tr', { numeric: true });
            }
        });
        
        rows.forEach(row => tbody.appendChild(row));
    },

    // Format functions
    formatNumber: function(num) {
        return new Intl.NumberFormat(this.config.locale).format(num);
    },

    formatCurrency: function(amount) {
        return new Intl.NumberFormat(this.config.locale, {
            style: 'currency',
            currency: this.config.currency
        }).format(amount);
    },

    formatDate: function(date) {
        return new Intl.DateTimeFormat(this.config.locale).format(new Date(date));
    },

    formatDateTime: function(date) {
        return new Intl.DateTimeFormat(this.config.locale, {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        }).format(new Date(date));
    },

    // AJAX helpers
    get: function(url) {
        return fetch(url, {
            method: 'GET',
            headers: {
                'Accept': 'application/json'
            }
        }).then(response => response.json());
    },

    post: function(url, data) {
        return fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify(data)
        }).then(response => response.json());
    },

    put: function(url, data) {
        return fetch(url, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify(data)
        }).then(response => response.json());
    },

    delete: function(url) {
        return fetch(url, {
            method: 'DELETE',
            headers: {
                'Accept': 'application/json'
            }
        }).then(response => response.json());
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    Admin.init();
});

// Export for global use
window.Admin = Admin;
