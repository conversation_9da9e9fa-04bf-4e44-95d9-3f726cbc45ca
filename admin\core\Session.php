<?php

namespace Core;

/**
 * Session Management Class
 * Otel Yönetim <PERSON> - Admin Panel
 */
class Session
{
    private $config;
    private $started = false;
    
    public function __construct($config)
    {
        $this->config = $config;
    }
    
    public function start()
    {
        if ($this->started) {
            return;
        }
        
        // Configure session settings
        ini_set('session.cookie_lifetime', $this->config['lifetime'] * 60);
        ini_set('session.cookie_httponly', $this->config['http_only']);
        ini_set('session.cookie_secure', $this->config['secure']);
        ini_set('session.cookie_samesite', $this->config['same_site']);
        ini_set('session.use_strict_mode', 1);
        ini_set('session.cookie_path', $this->config['path']);
        
        if ($this->config['domain']) {
            ini_set('session.cookie_domain', $this->config['domain']);
        }
        
        // Set session name
        session_name($this->config['cookie']);
        
        // Start session
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        $this->started = true;
        
        // Regenerate session ID periodically for security
        if (!$this->has('_last_regenerated') || 
            (time() - $this->get('_last_regenerated')) > 300) {
            $this->regenerate();
        }
        
        // Generate CSRF token if not exists
        if (!$this->has('_csrf_token')) {
            $this->generateCSRFToken();
        }
    }
    
    public function get($key, $default = null)
    {
        return $_SESSION[$key] ?? $default;
    }
    
    public function set($key, $value)
    {
        $_SESSION[$key] = $value;
    }
    
    public function has($key)
    {
        return isset($_SESSION[$key]);
    }
    
    public function remove($key)
    {
        unset($_SESSION[$key]);
    }
    
    public function clear()
    {
        $_SESSION = [];
    }
    
    public function destroy()
    {
        if ($this->started) {
            session_destroy();
            $this->started = false;
        }
    }
    
    public function regenerate($deleteOld = true)
    {
        session_regenerate_id($deleteOld);
        $this->set('_last_regenerated', time());
    }
    
    public function generateCSRFToken()
    {
        $token = bin2hex(random_bytes(32));
        $this->set('_csrf_token', $token);
        return $token;
    }
    
    public function getCSRFToken()
    {
        return $this->get('_csrf_token');
    }
    
    public function validateCSRFToken($token)
    {
        $sessionToken = $this->getCSRFToken();
        return $sessionToken && hash_equals($sessionToken, $token);
    }
    
    public function flash($key, $value = null)
    {
        if ($value === null) {
            // Get flash message
            $message = $this->get("_flash_{$key}");
            $this->remove("_flash_{$key}");
            return $message;
        } else {
            // Set flash message
            $this->set("_flash_{$key}", $value);
        }
    }
    
    public function hasFlash($key)
    {
        return $this->has("_flash_{$key}");
    }
    
    public function getFlashMessages()
    {
        $messages = [];
        foreach ($_SESSION as $key => $value) {
            if (strpos($key, '_flash_') === 0) {
                $messageKey = substr($key, 7);
                $messages[$messageKey] = $value;
                $this->remove($key);
            }
        }
        return $messages;
    }
    
    // Authentication helpers
    public function login($user)
    {
        $this->set('user_id', $user['id']);
        $this->set('user_email', $user['email']);
        $this->set('user_name', $user['name']);
        $this->set('user_role', $user['role']);
        $this->set('logged_in', true);
        $this->set('login_time', time());
        
        // Regenerate session ID for security
        $this->regenerate();
    }
    
    public function logout()
    {
        $this->remove('user_id');
        $this->remove('user_email');
        $this->remove('user_name');
        $this->remove('user_role');
        $this->remove('logged_in');
        $this->remove('login_time');
        
        // Regenerate session ID
        $this->regenerate();
    }
    
    public function isLoggedIn()
    {
        return $this->get('logged_in', false);
    }
    
    public function getUser()
    {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        return [
            'id' => $this->get('user_id'),
            'email' => $this->get('user_email'),
            'name' => $this->get('user_name'),
            'role' => $this->get('user_role'),
            'login_time' => $this->get('login_time'),
        ];
    }
    
    public function getUserId()
    {
        return $this->get('user_id');
    }
    
    public function getUserRole()
    {
        return $this->get('user_role');
    }
    
    public function hasRole($role)
    {
        return $this->getUserRole() === $role;
    }
    
    public function hasPermission($permission)
    {
        $userRole = $this->getUserRole();
        
        // Define role permissions
        $permissions = [
            'super_admin' => ['*'],
            'admin' => [
                'dashboard.view', 'users.manage', 'hotels.manage', 
                'reservations.manage', 'content.manage', 'media.manage',
                'system.settings', 'reports.view'
            ],
            'manager' => [
                'dashboard.view', 'hotels.manage', 'reservations.manage',
                'content.view', 'reports.view'
            ],
            'staff' => [
                'dashboard.view', 'reservations.view', 'content.view'
            ],
        ];
        
        if (!isset($permissions[$userRole])) {
            return false;
        }
        
        $rolePermissions = $permissions[$userRole];
        
        // Super admin has all permissions
        if (in_array('*', $rolePermissions)) {
            return true;
        }
        
        return in_array($permission, $rolePermissions);
    }
    
    public function getId()
    {
        return session_id();
    }
    
    public function isExpired()
    {
        $loginTime = $this->get('login_time');
        if (!$loginTime) {
            return true;
        }
        
        $maxLifetime = $this->config['lifetime'] * 60;
        return (time() - $loginTime) > $maxLifetime;
    }
}
