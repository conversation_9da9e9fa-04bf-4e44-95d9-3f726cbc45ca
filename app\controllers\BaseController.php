<?php

namespace App\Controllers;

use Core\Application;

/**
 * Base Controller Class
 * Otel Yönetim Sistemi
 */
abstract class BaseController
{
    protected $app;
    protected $db;
    protected $session;
    protected $config;
    
    public function __construct()
    {
        $this->app = Application::getInstance();
        $this->db = $this->app->getDatabase();
        $this->session = $this->app->getSession();
        $this->config = $this->app->getConfig();
    }
    
    /**
     * Render a view
     */
    protected function view($view, $data = [], $layout = null)
    {
        // Extract data to variables
        extract($data);
        
        // Add common variables
        $session = $this->session;
        $config = $this->config;
        $app = $this->app;
        
        // Determine layout
        if ($layout === null) {
            $layout = $this->app->isAdmin() ? 'layouts.admin' : 'layouts.frontend';
        }
        
        // Start output buffering
        ob_start();
        
        // Include the view file
        $viewFile = $this->getViewPath($view);
        if (file_exists($viewFile)) {
            include $viewFile;
        } else {
            throw new \Exception("View file not found: {$viewFile}");
        }
        
        // Get the view content
        $content = ob_get_clean();
        
        // If no layout, return content directly
        if ($layout === false) {
            echo $content;
            return;
        }
        
        // Include layout
        $layoutFile = $this->getViewPath($layout);
        if (file_exists($layoutFile)) {
            include $layoutFile;
        } else {
            echo $content; // Fallback to content only
        }
    }
    
    /**
     * Get view file path
     */
    private function getViewPath($view)
    {
        $view = str_replace('.', '/', $view);
        return APP_PATH . '/views/' . $view . '.php';
    }
    
    /**
     * Return JSON response
     */
    protected function json($data, $status = 200)
    {
        http_response_code($status);
        header('Content-Type: application/json');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * Redirect to a URL
     */
    protected function redirect($url, $status = 302)
    {
        header("Location: {$url}", true, $status);
        exit;
    }
    
    /**
     * Redirect back with input
     */
    protected function back($input = [])
    {
        $referer = $_SERVER['HTTP_REFERER'] ?? '/';
        
        if (!empty($input)) {
            $this->session->flash('old_input', $input);
        }
        
        $this->redirect($referer);
    }
    
    /**
     * Get old input value
     */
    protected function old($key, $default = '')
    {
        $oldInput = $this->session->flash('old_input') ?? [];
        return $oldInput[$key] ?? $default;
    }
    
    /**
     * Validate request data
     */
    protected function validate($data, $rules)
    {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? null;
            $fieldRules = explode('|', $rule);
            
            foreach ($fieldRules as $fieldRule) {
                $ruleParts = explode(':', $fieldRule);
                $ruleName = $ruleParts[0];
                $ruleValue = $ruleParts[1] ?? null;
                
                switch ($ruleName) {
                    case 'required':
                        if (empty($value)) {
                            $errors[$field][] = ucfirst($field) . ' alanı zorunludur.';
                        }
                        break;
                        
                    case 'email':
                        if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                            $errors[$field][] = ucfirst($field) . ' geçerli bir email adresi olmalıdır.';
                        }
                        break;
                        
                    case 'min':
                        if (!empty($value) && strlen($value) < $ruleValue) {
                            $errors[$field][] = ucfirst($field) . " en az {$ruleValue} karakter olmalıdır.";
                        }
                        break;
                        
                    case 'max':
                        if (!empty($value) && strlen($value) > $ruleValue) {
                            $errors[$field][] = ucfirst($field) . " en fazla {$ruleValue} karakter olmalıdır.";
                        }
                        break;
                        
                    case 'unique':
                        if (!empty($value)) {
                            $tableParts = explode(',', $ruleValue);
                            $table = $tableParts[0];
                            $column = $tableParts[1] ?? $field;
                            $ignoreId = $tableParts[2] ?? null;
                            
                            $query = "SELECT COUNT(*) as count FROM {$table} WHERE {$column} = :value";
                            $params = ['value' => $value];
                            
                            if ($ignoreId) {
                                $query .= " AND id != :ignore_id";
                                $params['ignore_id'] = $ignoreId;
                            }
                            
                            $result = $this->db->selectOne($query, $params);
                            if ($result['count'] > 0) {
                                $errors[$field][] = ucfirst($field) . ' zaten kullanılmaktadır.';
                            }
                        }
                        break;
                }
            }
        }
        
        if (!empty($errors)) {
            $this->session->flash('errors', $errors);
            $this->session->flash('old_input', $data);
            return false;
        }
        
        return true;
    }
    
    /**
     * Get validation errors
     */
    protected function errors($field = null)
    {
        $errors = $this->session->flash('errors') ?? [];
        
        if ($field) {
            return $errors[$field] ?? [];
        }
        
        return $errors;
    }
    
    /**
     * Check if user is authenticated
     */
    protected function requireAuth()
    {
        if (!$this->session->isLoggedIn()) {
            $this->session->flash('error', 'Bu sayfaya erişmek için giriş yapmalısınız.');
            $this->redirect('/admin/login');
        }
    }
    
    /**
     * Check if user has permission
     */
    protected function requirePermission($permission)
    {
        $this->requireAuth();
        
        if (!$this->session->hasPermission($permission)) {
            $this->session->flash('error', 'Bu işlem için yetkiniz bulunmamaktadır.');
            $this->redirect('/admin/dashboard');
        }
    }
    
    /**
     * Get current user
     */
    protected function user()
    {
        return $this->session->getUser();
    }
    
    /**
     * Upload file
     */
    protected function uploadFile($file, $path = 'uploads', $allowedTypes = null)
    {
        if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
            return false;
        }
        
        $allowedTypes = $allowedTypes ?? $this->config['app']['upload']['allowed_extensions'];
        $maxSize = $this->config['app']['upload']['max_file_size'];
        
        // Check file size
        if ($file['size'] > $maxSize) {
            throw new \Exception('Dosya boyutu çok büyük.');
        }
        
        // Check file type
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $allowedTypes)) {
            throw new \Exception('Dosya türü desteklenmiyor.');
        }
        
        // Generate unique filename
        $filename = uniqid() . '.' . $extension;
        $uploadPath = STORAGE_PATH . '/uploads/' . $path;
        
        // Create directory if not exists
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }
        
        $fullPath = $uploadPath . '/' . $filename;
        
        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $fullPath)) {
            return $path . '/' . $filename;
        }
        
        return false;
    }
}
