<?php
$user = $session->getUser();
$notifications = []; // This would come from a notification service
$unreadCount = count($notifications);
?>

<div class="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
    <!-- Mobile menu button -->
    <button type="button" class="-m-2.5 p-2.5 text-gray-700 lg:hidden" @click="sidebarOpen = true">
        <span class="sr-only">Open sidebar</span>
        <i class="fas fa-bars h-5 w-5"></i>
    </button>

    <!-- Separator -->
    <div class="h-6 w-px bg-gray-200 lg:hidden"></div>

    <div class="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
        <!-- Search -->
        <form class="relative flex flex-1" action="#" method="GET">
            <label for="search-field" class="sr-only">Search</label>
            <i class="fas fa-search pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 pl-3 flex items-center"></i>
            <input id="search-field" class="block h-full w-full border-0 py-0 pl-10 pr-0 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Ara..." type="search" name="search">
        </form>
        
        <div class="flex items-center gap-x-4 lg:gap-x-6">
            <!-- Notifications -->
            <div class="relative" x-data="{ open: false }">
                <button type="button" class="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500 relative" @click="open = !open">
                    <span class="sr-only">View notifications</span>
                    <i class="fas fa-bell h-6 w-6"></i>
                    <?php if ($unreadCount > 0): ?>
                        <span class="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-red-500 text-xs text-white flex items-center justify-center">
                            <?= $unreadCount > 9 ? '9+' : $unreadCount ?>
                        </span>
                    <?php endif; ?>
                </button>
                
                <!-- Notifications dropdown -->
                <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95" class="absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none" style="display: none;">
                    <div class="px-4 py-2 border-b border-gray-200">
                        <h3 class="text-sm font-medium text-gray-900">Bildirimler</h3>
                    </div>
                    
                    <?php if (empty($notifications)): ?>
                        <div class="px-4 py-3 text-sm text-gray-500 text-center">
                            Yeni bildirim yok
                        </div>
                    <?php else: ?>
                        <div class="max-h-64 overflow-y-auto">
                            <?php foreach ($notifications as $notification): ?>
                                <a href="#" class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-info-circle h-5 w-5 text-blue-500"></i>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <p class="font-medium"><?= htmlspecialchars($notification['title']) ?></p>
                                            <p class="text-gray-500"><?= htmlspecialchars($notification['message']) ?></p>
                                            <p class="text-xs text-gray-400 mt-1"><?= $notification['time'] ?></p>
                                        </div>
                                    </div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                        <div class="border-t border-gray-200 px-4 py-2">
                            <a href="/admin/notifications" class="text-sm text-primary-600 hover:text-primary-500">
                                Tüm bildirimleri görüntüle
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="relative" x-data="{ open: false }">
                <button type="button" class="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500" @click="open = !open">
                    <span class="sr-only">Quick actions</span>
                    <i class="fas fa-plus h-6 w-6"></i>
                </button>
                
                <!-- Quick actions dropdown -->
                <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95" class="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none" style="display: none;">
                    <a href="/admin/hotel/reservations/create" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                        <i class="fas fa-calendar-plus mr-2"></i>
                        Yeni Rezervasyon
                    </a>
                    <a href="/admin/hotel/rooms/create" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                        <i class="fas fa-bed mr-2"></i>
                        Yeni Oda
                    </a>
                    <a href="/admin/content/blog/create" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                        <i class="fas fa-edit mr-2"></i>
                        Yeni Blog Yazısı
                    </a>
                    <a href="/admin/content/pages/create" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                        <i class="fas fa-file-alt mr-2"></i>
                        Yeni Sayfa
                    </a>
                </div>
            </div>

            <!-- Separator -->
            <div class="hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200"></div>

            <!-- Profile dropdown -->
            <div class="relative" x-data="{ open: false }">
                <button type="button" class="-m-1.5 flex items-center p-1.5" @click="open = !open">
                    <span class="sr-only">Open user menu</span>
                    <div class="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center">
                        <i class="fas fa-user text-white text-sm"></i>
                    </div>
                    <span class="hidden lg:flex lg:items-center">
                        <span class="ml-4 text-sm font-semibold leading-6 text-gray-900"><?= htmlspecialchars($user['name']) ?></span>
                        <i class="fas fa-chevron-down ml-2 h-5 w-5 text-gray-400"></i>
                    </span>
                </button>
                
                <!-- Profile dropdown menu -->
                <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95" class="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none" style="display: none;">
                    <div class="px-4 py-2 border-b border-gray-200">
                        <p class="text-sm font-medium text-gray-900"><?= htmlspecialchars($user['name']) ?></p>
                        <p class="text-xs text-gray-500"><?= htmlspecialchars($user['email']) ?></p>
                        <p class="text-xs text-gray-400"><?= ucfirst($user['role']) ?></p>
                    </div>
                    
                    <a href="/admin/profile" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                        <i class="fas fa-user mr-2"></i>
                        Profil
                    </a>
                    <a href="/admin/profile/settings" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                        <i class="fas fa-cog mr-2"></i>
                        Ayarlar
                    </a>
                    <a href="/admin/help" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                        <i class="fas fa-question-circle mr-2"></i>
                        Yardım
                    </a>
                    
                    <div class="border-t border-gray-200">
                        <form method="POST" action="/admin/logout">
                            <input type="hidden" name="_token" value="<?= $session->getCSRFToken() ?>">
                            <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <i class="fas fa-sign-out-alt mr-2"></i>
                                Çıkış Yap
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
