Otel Yönetim Sistemi Admin Paneli
1. <PERSON><PERSON> (Dashboard)
Ana Panel: Kullanı<PERSON>ı dostu arayüz ile genel durum ve istatistiklerin sunulması.
Önemli İstatistikler: Rezervasyon sayısı, do<PERSON><PERSON> oran<PERSON>, gelir bilgileri gibi verilerin grafiksel gösterimi.
2. <PERSON><PERSON> Login Ekranı
Giriş İşlevi: Kullanıcı adı ve şifre ile kimlik doğrulama.
Güvenlik Özellikleri:
Şifrelerin güvenli bir şekilde saklanması ve doğrulama.
Yanlış girişlerde uygun hata mesajları gösterimi.
Brute force saldırılarına karşı güvenlik soruları veya CAPTCHA.
Kullanıcı Yönetimi: Farklı kullanıcı rolleri ve yetkilendirme.
Oturum Yönetimi: Oturum açma, kapatma ve otomatik oturum süresi.
UI/UX Tasarımı: Modern ve kullanıcı dostu bi<PERSON>, Tailwind CSS ile responsive tasarım.
3. İçerik Yönetimi
Sayfalar: Statik sayfa<PERSON>ın (<PERSON>, Hakkım<PERSON>zda, İletişim vb.) eklenmesi, düzenlenmesi ve silinmesi.
Blog: Blog yazıları oluşturma, düzenleme ve silme.
Menü: Navigasyon menüsünün yönetimi; yeni menü öğeleri ekleme ve mevcut öğeleri düzenleme.
4. Medya Yönetimi
Slider: Anasayfada kullanılan slider görsellerinin eklenmesi ve düzenlenmesi.
Banner: Reklam banner'larının eklenmesi, düzenlenmesi ve silinmesi.
Galeri: Fotoğraf galerisi oluşturma, düzenleme ve silme.
5. Otel İşlemleri
Odalar: Oda bilgileri ekleme, düzenleme ve silme.
Oda Kategorileri: Oda türleri (standart, lüks vb.) oluşturma ve düzenleme.
Oda Özellikleri: Oda ile ilgili özelliklerin (Wi-Fi, TV, vb.) tanımlanması.
Oda Musaitliği: Oda durumunun (boş/dolu) kontrolü ve güncellenmesi.
Rezervasyonlar: Müşteri rezervasyonlarının alınması ve yönetilmesi.
Rezervasyon Listesi: Tüm rezervasyonların görüntülenmesi, düzenlenmesi ve silinmesi.
6. Entegrasyon
Ödeme Sistemleri: Online ödeme sistemleri (Kredi kartı, PayPal vb.) ile entegrasyon.
Google Analytics: Site trafiğini izlemek için entegrasyon.
Google Tag Manager: Etiket yönetimi için entegrasyon.
Google Search Console: SEO analizi ve optimizasyonu için entegrasyon.
7. Aktiviteler
Etkinlik Yönetimi: Otelde düzenlenen etkinliklerin eklenmesi, düzenlenmesi ve silinmesi.
8. Pazarlama & SEO
Kelime Analizi: Anahtar kelimelerin analiz edilmesi ve performans değerlendirmesi.
Kelime Üreticisi: SEO için anahtar kelime önerileri.
Promosyonlar: Özel kampanya ve indirimlerin yönetimi.
Sponsorlar: Sponsor anlaşmalarının kaydedilmesi ve yönetimi.
9. Sistem Ayarları
Genel Ayarlar: Site ayarlarının yönetimi (site adı, iletişim bilgileri vb.).
Kullanıcı Yönetimi: Kullanıcı hesapları oluşturma, düzenleme ve silme.
IP Ayarları: Erişim kontrolü için IP yönetimi.
Dil Yönetimi: Çok dilli destek; Türkçe ve İngilizce dil seçenekleri.
Dil seçeneği ile içeriklerin otomatik olarak güncellenmesi.
İstatistikler: Kullanıcı ve rezervasyon istatistiklerinin görüntülenmesi.
10. Ek Özellikler
Hata Yönetimi: Hataları loglama ve yönetme.
Yedekleme: Veritabanı ve dosya yedekleme mekanizmaları.
Güvenlik Ayarları: Kullanıcı erişim izinleri ve yetkilendirme.
11. Kullanıcı Destek
Sıkça Sorulan Sorular (SSS): Kullanıcıların sık karşılaştığı sorunlar için rehber.
İletişim Formu: Kullanıcıların destek talep edebilmesi için iletişim formu.
Kullanılacak Teknolojiler
Backend: PHP, MVC yapısı.
Frontend: HTML, CSS (Tailwind CSS).
Veritabanı: MySQL