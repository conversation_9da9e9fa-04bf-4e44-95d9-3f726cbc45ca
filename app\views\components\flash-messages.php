<?php
$flashMessages = $session->getFlashMessages();

if (!empty($flashMessages)):
?>
<div class="mb-6 space-y-4">
    <?php foreach ($flashMessages as $type => $message): ?>
        <?php
        $alertClasses = [
            'success' => 'bg-green-50 border-green-200 text-green-800',
            'error' => 'bg-red-50 border-red-200 text-red-800',
            'warning' => 'bg-yellow-50 border-yellow-200 text-yellow-800',
            'info' => 'bg-blue-50 border-blue-200 text-blue-800'
        ];
        
        $iconClasses = [
            'success' => 'fas fa-check-circle text-green-400',
            'error' => 'fas fa-exclamation-circle text-red-400',
            'warning' => 'fas fa-exclamation-triangle text-yellow-400',
            'info' => 'fas fa-info-circle text-blue-400'
        ];
        
        $alertClass = $alertClasses[$type] ?? $alertClasses['info'];
        $iconClass = $iconClasses[$type] ?? $iconClasses['info'];
        ?>
        
        <div class="alert-auto-hide rounded-md border p-4 <?= $alertClass ?>" x-data="{ show: true }" x-show="show" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform scale-90" x-transition:enter-end="opacity-100 transform scale-100" x-transition:leave="transition ease-in duration-300" x-transition:leave-start="opacity-100 transform scale-100" x-transition:leave-end="opacity-0 transform scale-90">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="<?= $iconClass ?> h-5 w-5"></i>
                </div>
                <div class="ml-3 flex-1">
                    <p class="text-sm font-medium">
                        <?= htmlspecialchars($message) ?>
                    </p>
                </div>
                <div class="ml-auto pl-3">
                    <div class="-mx-1.5 -my-1.5">
                        <button type="button" @click="show = false" class="inline-flex rounded-md p-1.5 hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-offset-2">
                            <span class="sr-only">Dismiss</span>
                            <i class="fas fa-times h-5 w-5"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
</div>
<?php endif; ?>

<!-- Toast Notifications Container -->
<div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2">
    <!-- Toast notifications will be dynamically added here -->
</div>

<script>
// Toast notification function
function showToast(message, type = 'info', duration = 5000) {
    const container = document.getElementById('toast-container');
    
    const toastClasses = {
        success: 'bg-green-500 text-white',
        error: 'bg-red-500 text-white',
        warning: 'bg-yellow-500 text-white',
        info: 'bg-blue-500 text-white'
    };
    
    const iconClasses = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };
    
    const toast = document.createElement('div');
    toast.className = `flex items-center p-4 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full opacity-0 ${toastClasses[type] || toastClasses.info}`;
    
    toast.innerHTML = `
        <div class="flex-shrink-0">
            <i class="${iconClasses[type] || iconClasses.info} h-5 w-5"></i>
        </div>
        <div class="ml-3 flex-1">
            <p class="text-sm font-medium">${message}</p>
        </div>
        <div class="ml-4">
            <button type="button" onclick="this.parentElement.parentElement.remove()" class="text-white hover:text-gray-200">
                <i class="fas fa-times h-4 w-4"></i>
            </button>
        </div>
    `;
    
    container.appendChild(toast);
    
    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full', 'opacity-0');
        toast.classList.add('translate-x-0', 'opacity-100');
    }, 100);
    
    // Auto remove
    setTimeout(() => {
        toast.classList.add('translate-x-full', 'opacity-0');
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 300);
    }, duration);
}

// Global toast functions
window.showSuccessToast = (message, duration) => showToast(message, 'success', duration);
window.showErrorToast = (message, duration) => showToast(message, 'error', duration);
window.showWarningToast = (message, duration) => showToast(message, 'warning', duration);
window.showInfoToast = (message, duration) => showToast(message, 'info', duration);
</script>
