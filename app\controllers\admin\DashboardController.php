<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\User;

/**
 * Admin Dashboard Controller
 * Otel Yönetim <PERSON>
 */
class DashboardController extends BaseController
{
    /**
     * Show dashboard
     */
    public function index()
    {
        $this->requireAuth();
        
        // Get dashboard statistics
        $stats = $this->getDashboardStats();
        
        // Get recent activities
        $recentActivities = $this->getRecentActivities();
        
        // Get quick stats
        $quickStats = $this->getQuickStats();
        
        // Get chart data
        $chartData = $this->getChartData();
        
        $this->view('admin.dashboard.index', [
            'title' => 'Dashboard',
            'stats' => $stats,
            'recentActivities' => $recentActivities,
            'quickStats' => $quickStats,
            'chartData' => $chartData,
            'user' => $this->user()
        ], 'layouts.admin');
    }
    
    /**
     * Get dashboard statistics
     */
    private function getDashboardStats()
    {
        $stats = [];
        
        try {
            // Total reservations
            $stats['total_reservations'] = $this->db->selectOne(
                "SELECT COUNT(*) as count FROM reservations"
            )['count'] ?? 0;
            
            // Today's reservations
            $stats['today_reservations'] = $this->db->selectOne(
                "SELECT COUNT(*) as count FROM reservations WHERE DATE(created_at) = CURDATE()"
            )['count'] ?? 0;
            
            // Total rooms
            $stats['total_rooms'] = $this->db->selectOne(
                "SELECT COUNT(*) as count FROM rooms"
            )['count'] ?? 0;
            
            // Available rooms
            $stats['available_rooms'] = $this->db->selectOne(
                "SELECT COUNT(*) as count FROM rooms WHERE status = 'available'"
            )['count'] ?? 0;
            
            // Total revenue (this month)
            $stats['monthly_revenue'] = $this->db->selectOne(
                "SELECT COALESCE(SUM(total_amount), 0) as revenue 
                 FROM reservations 
                 WHERE MONTH(created_at) = MONTH(CURDATE()) 
                 AND YEAR(created_at) = YEAR(CURDATE())
                 AND status = 'confirmed'"
            )['revenue'] ?? 0;
            
            // Occupancy rate
            $occupiedRooms = $this->db->selectOne(
                "SELECT COUNT(*) as count FROM rooms WHERE status = 'occupied'"
            )['count'] ?? 0;
            
            $stats['occupancy_rate'] = $stats['total_rooms'] > 0 
                ? round(($occupiedRooms / $stats['total_rooms']) * 100, 1) 
                : 0;
            
            // Pending reservations
            $stats['pending_reservations'] = $this->db->selectOne(
                "SELECT COUNT(*) as count FROM reservations WHERE status = 'pending'"
            )['count'] ?? 0;
            
            // Active users
            $stats['active_users'] = $this->db->selectOne(
                "SELECT COUNT(*) as count FROM users WHERE status = 'active'"
            )['count'] ?? 0;
            
        } catch (\Exception $e) {
            // If tables don't exist yet, return default values
            $stats = [
                'total_reservations' => 0,
                'today_reservations' => 0,
                'total_rooms' => 0,
                'available_rooms' => 0,
                'monthly_revenue' => 0,
                'occupancy_rate' => 0,
                'pending_reservations' => 0,
                'active_users' => 0
            ];
        }
        
        return $stats;
    }
    
    /**
     * Get recent activities
     */
    private function getRecentActivities()
    {
        $activities = [];
        
        try {
            // Recent reservations
            $recentReservations = $this->db->select(
                "SELECT 'reservation' as type, id, guest_name as title, created_at, status
                 FROM reservations 
                 ORDER BY created_at DESC 
                 LIMIT 5"
            );
            
            foreach ($recentReservations as $reservation) {
                $activities[] = [
                    'type' => 'reservation',
                    'icon' => 'fas fa-calendar-check',
                    'title' => 'Yeni Rezervasyon: ' . $reservation['title'],
                    'time' => $reservation['created_at'],
                    'status' => $reservation['status']
                ];
            }
            
            // Recent user logins
            $recentLogins = $this->db->select(
                "SELECT 'login' as type, name as title, last_login as created_at
                 FROM users 
                 WHERE last_login IS NOT NULL 
                 ORDER BY last_login DESC 
                 LIMIT 3"
            );
            
            foreach ($recentLogins as $login) {
                $activities[] = [
                    'type' => 'login',
                    'icon' => 'fas fa-sign-in-alt',
                    'title' => $login['title'] . ' giriş yaptı',
                    'time' => $login['created_at'],
                    'status' => 'success'
                ];
            }
            
            // Sort by time
            usort($activities, function($a, $b) {
                return strtotime($b['time']) - strtotime($a['time']);
            });
            
            $activities = array_slice($activities, 0, 10);
            
        } catch (\Exception $e) {
            $activities = [];
        }
        
        return $activities;
    }
    
    /**
     * Get quick stats for cards
     */
    private function getQuickStats()
    {
        $stats = $this->getDashboardStats();
        
        return [
            [
                'title' => 'Toplam Rezervasyon',
                'value' => number_format($stats['total_reservations']),
                'icon' => 'fas fa-calendar-check',
                'color' => 'blue',
                'change' => '+12%',
                'changeType' => 'increase'
            ],
            [
                'title' => 'Bugünkü Rezervasyonlar',
                'value' => number_format($stats['today_reservations']),
                'icon' => 'fas fa-calendar-day',
                'color' => 'green',
                'change' => '+5%',
                'changeType' => 'increase'
            ],
            [
                'title' => 'Doluluk Oranı',
                'value' => $stats['occupancy_rate'] . '%',
                'icon' => 'fas fa-chart-pie',
                'color' => 'yellow',
                'change' => '+8%',
                'changeType' => 'increase'
            ],
            [
                'title' => 'Aylık Gelir',
                'value' => '₺' . number_format($stats['monthly_revenue'], 2),
                'icon' => 'fas fa-money-bill-wave',
                'color' => 'purple',
                'change' => '+15%',
                'changeType' => 'increase'
            ]
        ];
    }
    
    /**
     * Get chart data
     */
    private function getChartData()
    {
        $chartData = [];
        
        try {
            // Revenue chart data (last 12 months)
            $revenueData = $this->db->select(
                "SELECT 
                    DATE_FORMAT(created_at, '%Y-%m') as month,
                    COALESCE(SUM(total_amount), 0) as revenue
                 FROM reservations 
                 WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
                 AND status = 'confirmed'
                 GROUP BY DATE_FORMAT(created_at, '%Y-%m')
                 ORDER BY month"
            );
            
            $chartData['revenue'] = [
                'labels' => array_column($revenueData, 'month'),
                'data' => array_column($revenueData, 'revenue')
            ];
            
            // Reservations chart data (last 7 days)
            $reservationData = $this->db->select(
                "SELECT 
                    DATE(created_at) as date,
                    COUNT(*) as count
                 FROM reservations 
                 WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
                 GROUP BY DATE(created_at)
                 ORDER BY date"
            );
            
            $chartData['reservations'] = [
                'labels' => array_column($reservationData, 'date'),
                'data' => array_column($reservationData, 'count')
            ];
            
            // Room status distribution
            $roomStatusData = $this->db->select(
                "SELECT status, COUNT(*) as count FROM rooms GROUP BY status"
            );
            
            $chartData['roomStatus'] = [
                'labels' => array_column($roomStatusData, 'status'),
                'data' => array_column($roomStatusData, 'count')
            ];
            
        } catch (\Exception $e) {
            $chartData = [
                'revenue' => ['labels' => [], 'data' => []],
                'reservations' => ['labels' => [], 'data' => []],
                'roomStatus' => ['labels' => [], 'data' => []]
            ];
        }
        
        return $chartData;
    }
}
