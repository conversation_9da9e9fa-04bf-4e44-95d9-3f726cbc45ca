<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-auto flex justify-center">
                <img class="h-12 w-auto" src="/assets/images/logo.png" alt="Otel Yönetim Sistemi">
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Admin Panel Girişi
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Otel Yönetim Sistemi
            </p>
        </div>
        
        <form class="mt-8 space-y-6" action="/admin/login" method="POST">
            <input type="hidden" name="_token" value="<?= $session->getCSRFToken() ?>">
            
            <div class="rounded-md shadow-sm -space-y-px">
                <div>
                    <label for="email" class="sr-only">Email adresi</label>
                    <input id="email" name="email" type="email" autocomplete="email" required 
                           class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm" 
                           placeholder="Email adresi" 
                           value="<?= htmlspecialchars($session->flash('old_input')['email'] ?? '') ?>">
                </div>
                <div>
                    <label for="password" class="sr-only">Şifre</label>
                    <input id="password" name="password" type="password" autocomplete="current-password" required 
                           class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm" 
                           placeholder="Şifre">
                </div>
            </div>

            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <input id="remember" name="remember" type="checkbox" 
                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                    <label for="remember" class="ml-2 block text-sm text-gray-900">
                        Beni hatırla
                    </label>
                </div>

                <div class="text-sm">
                    <a href="/admin/forgot-password" class="font-medium text-indigo-600 hover:text-indigo-500">
                        Şifremi unuttum
                    </a>
                </div>
            </div>

            <div>
                <button type="submit" 
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <i class="fas fa-lock h-5 w-5 text-indigo-500 group-hover:text-indigo-400"></i>
                    </span>
                    Giriş Yap
                </button>
            </div>
            
            <!-- Demo Credentials -->
            <div class="mt-6 p-4 bg-blue-50 rounded-md">
                <h3 class="text-sm font-medium text-blue-800 mb-2">Demo Hesapları:</h3>
                <div class="text-xs text-blue-700 space-y-1">
                    <div><strong>Super Admin:</strong> <EMAIL> / admin123</div>
                    <div><strong>Manager:</strong> <EMAIL> / manager123</div>
                    <div><strong>Staff:</strong> <EMAIL> / staff123</div>
                </div>
            </div>
        </form>
        
        <!-- Security Notice -->
        <div class="mt-6 text-center">
            <p class="text-xs text-gray-500">
                <i class="fas fa-shield-alt mr-1"></i>
                Bu sistem güvenli SSL bağlantısı kullanmaktadır
            </p>
        </div>
    </div>
</div>

<script>
// Auto-focus on email field
document.getElementById('email').focus();

// Show/hide password
function togglePassword() {
    const passwordField = document.getElementById('password');
    const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
    passwordField.setAttribute('type', type);
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    
    if (!email || !password) {
        e.preventDefault();
        alert('Lütfen tüm alanları doldurun.');
        return false;
    }
    
    if (!email.includes('@')) {
        e.preventDefault();
        alert('Lütfen geçerli bir email adresi girin.');
        return false;
    }
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Giriş yapılıyor...';
    submitBtn.disabled = true;
});

// Demo credential quick fill
document.addEventListener('DOMContentLoaded', function() {
    const demoCredentials = document.querySelectorAll('[data-demo]');
    demoCredentials.forEach(function(element) {
        element.addEventListener('click', function() {
            const email = this.getAttribute('data-email');
            const password = this.getAttribute('data-password');
            
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
        });
    });
});
</script>
