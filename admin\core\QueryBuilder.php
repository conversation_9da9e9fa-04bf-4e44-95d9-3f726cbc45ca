<?php

namespace Core;

/**
 * Query Builder Class
 * Otel Yönetim <PERSON>mi - Admin Panel
 */
class QueryBuilder
{
    private $database;
    private $table;
    private $select = ['*'];
    private $where = [];
    private $joins = [];
    private $orderBy = [];
    private $groupBy = [];
    private $having = [];
    private $limit = null;
    private $offset = null;
    private $params = [];
    
    public function __construct(Database $database, $table)
    {
        $this->database = $database;
        $this->table = $table;
    }
    
    public function select($columns = ['*'])
    {
        if (is_string($columns)) {
            $columns = [$columns];
        }
        $this->select = $columns;
        return $this;
    }
    
    public function where($column, $operator = null, $value = null)
    {
        if (func_num_args() === 2) {
            $value = $operator;
            $operator = '=';
        }
        
        $placeholder = ':where_' . count($this->params);
        $this->where[] = "{$column} {$operator} {$placeholder}";
        $this->params[$placeholder] = $value;
        
        return $this;
    }
    
    public function whereIn($column, $values)
    {
        $placeholders = [];
        foreach ($values as $i => $value) {
            $placeholder = ':wherein_' . count($this->params) . '_' . $i;
            $placeholders[] = $placeholder;
            $this->params[$placeholder] = $value;
        }
        
        $this->where[] = "{$column} IN (" . implode(',', $placeholders) . ")";
        return $this;
    }
    
    public function whereBetween($column, $min, $max)
    {
        $minPlaceholder = ':between_min_' . count($this->params);
        $maxPlaceholder = ':between_max_' . count($this->params);
        
        $this->where[] = "{$column} BETWEEN {$minPlaceholder} AND {$maxPlaceholder}";
        $this->params[$minPlaceholder] = $min;
        $this->params[$maxPlaceholder] = $max;
        
        return $this;
    }
    
    public function whereNull($column)
    {
        $this->where[] = "{$column} IS NULL";
        return $this;
    }
    
    public function whereNotNull($column)
    {
        $this->where[] = "{$column} IS NOT NULL";
        return $this;
    }
    
    public function orWhere($column, $operator = null, $value = null)
    {
        if (func_num_args() === 2) {
            $value = $operator;
            $operator = '=';
        }
        
        $placeholder = ':orwhere_' . count($this->params);
        $this->where[] = "OR {$column} {$operator} {$placeholder}";
        $this->params[$placeholder] = $value;
        
        return $this;
    }
    
    public function join($table, $first, $operator = null, $second = null)
    {
        return $this->addJoin('INNER', $table, $first, $operator, $second);
    }
    
    public function leftJoin($table, $first, $operator = null, $second = null)
    {
        return $this->addJoin('LEFT', $table, $first, $operator, $second);
    }
    
    public function rightJoin($table, $first, $operator = null, $second = null)
    {
        return $this->addJoin('RIGHT', $table, $first, $operator, $second);
    }
    
    private function addJoin($type, $table, $first, $operator, $second)
    {
        if (func_num_args() === 4) {
            $second = $operator;
            $operator = '=';
        }
        
        $this->joins[] = "{$type} JOIN {$table} ON {$first} {$operator} {$second}";
        return $this;
    }
    
    public function orderBy($column, $direction = 'ASC')
    {
        $this->orderBy[] = "{$column} {$direction}";
        return $this;
    }
    
    public function groupBy($columns)
    {
        if (is_string($columns)) {
            $columns = [$columns];
        }
        $this->groupBy = array_merge($this->groupBy, $columns);
        return $this;
    }
    
    public function having($column, $operator = null, $value = null)
    {
        if (func_num_args() === 2) {
            $value = $operator;
            $operator = '=';
        }
        
        $placeholder = ':having_' . count($this->params);
        $this->having[] = "{$column} {$operator} {$placeholder}";
        $this->params[$placeholder] = $value;
        
        return $this;
    }
    
    public function limit($limit)
    {
        $this->limit = $limit;
        return $this;
    }
    
    public function offset($offset)
    {
        $this->offset = $offset;
        return $this;
    }
    
    public function paginate($page = 1, $perPage = 20)
    {
        $offset = ($page - 1) * $perPage;
        return $this->limit($perPage)->offset($offset);
    }
    
    public function get()
    {
        $sql = $this->buildSelectQuery();
        return $this->database->select($sql, $this->params);
    }
    
    public function first()
    {
        $sql = $this->buildSelectQuery();
        return $this->database->selectOne($sql, $this->params);
    }
    
    public function count()
    {
        $originalSelect = $this->select;
        $this->select = ['COUNT(*) as count'];
        
        $sql = $this->buildSelectQuery();
        $result = $this->database->selectOne($sql, $this->params);
        
        $this->select = $originalSelect;
        
        return $result ? (int)$result['count'] : 0;
    }
    
    private function buildSelectQuery()
    {
        $sql = 'SELECT ' . implode(', ', $this->select);
        $sql .= ' FROM ' . $this->table;
        
        if (!empty($this->joins)) {
            $sql .= ' ' . implode(' ', $this->joins);
        }
        
        if (!empty($this->where)) {
            $sql .= ' WHERE ' . implode(' AND ', $this->where);
        }
        
        if (!empty($this->groupBy)) {
            $sql .= ' GROUP BY ' . implode(', ', $this->groupBy);
        }
        
        if (!empty($this->having)) {
            $sql .= ' HAVING ' . implode(' AND ', $this->having);
        }
        
        if (!empty($this->orderBy)) {
            $sql .= ' ORDER BY ' . implode(', ', $this->orderBy);
        }
        
        if ($this->limit !== null) {
            $sql .= ' LIMIT ' . $this->limit;
        }
        
        if ($this->offset !== null) {
            $sql .= ' OFFSET ' . $this->offset;
        }
        
        return $sql;
    }
    
    public function insert($data)
    {
        return $this->database->insert($this->table, $data);
    }
    
    public function update($data)
    {
        if (empty($this->where)) {
            throw new \Exception('Update query must have WHERE clause');
        }
        
        $whereClause = implode(' AND ', $this->where);
        return $this->database->update($this->table, $data, $whereClause, $this->params);
    }
    
    public function delete()
    {
        if (empty($this->where)) {
            throw new \Exception('Delete query must have WHERE clause');
        }
        
        $whereClause = implode(' AND ', $this->where);
        return $this->database->delete($this->table, $whereClause, $this->params);
    }
}
