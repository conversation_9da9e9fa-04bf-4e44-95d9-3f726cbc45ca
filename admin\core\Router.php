<?php

namespace Core;

/**
 * Router Class
 * Otel Yönetim <PERSON> - Admin Panel
 */
class Router
{
    private $routes = [];
    private $middlewares = [];
    private $currentRoute = null;
    
    public function __construct()
    {
        $this->loadRoutes();
    }
    
    private function loadRoutes()
    {
        // Authentication routes
        $this->get('/login', 'Auth\LoginController@showLoginForm');
        $this->post('/login', 'Auth\LoginController@login');
        $this->post('/logout', 'Auth\LoginController@logout');
        
        // Dashboard routes (protected)
        $this->get('/', 'DashboardController@index', ['auth']);
        $this->get('/dashboard', 'DashboardController@index', ['auth']);
        
        // Hotel Management routes
        $this->group(['prefix' => '/hotel', 'middleware' => ['auth']], function() {
            // Rooms
            $this->get('/rooms', 'Hotel\RoomController@index');
            $this->get('/rooms/create', 'Hotel\RoomController@create');
            $this->post('/rooms', 'Hotel\RoomController@store');
            $this->get('/rooms/{id}', 'Hotel\RoomController@show');
            $this->get('/rooms/{id}/edit', 'Hotel\RoomController@edit');
            $this->put('/rooms/{id}', 'Hotel\RoomController@update');
            $this->delete('/rooms/{id}', 'Hotel\RoomController@destroy');
            
            // Room Categories
            $this->get('/categories', 'Hotel\CategoryController@index');
            $this->post('/categories', 'Hotel\CategoryController@store');
            $this->put('/categories/{id}', 'Hotel\CategoryController@update');
            $this->delete('/categories/{id}', 'Hotel\CategoryController@destroy');
            
            // Reservations
            $this->get('/reservations', 'Hotel\ReservationController@index');
            $this->get('/reservations/create', 'Hotel\ReservationController@create');
            $this->post('/reservations', 'Hotel\ReservationController@store');
            $this->get('/reservations/{id}', 'Hotel\ReservationController@show');
            $this->put('/reservations/{id}', 'Hotel\ReservationController@update');
            $this->delete('/reservations/{id}', 'Hotel\ReservationController@destroy');
        });
        
        // Content Management routes
        $this->group(['prefix' => '/content', 'middleware' => ['auth']], function() {
            // Pages
            $this->get('/pages', 'Content\PageController@index');
            $this->get('/pages/create', 'Content\PageController@create');
            $this->post('/pages', 'Content\PageController@store');
            $this->get('/pages/{id}/edit', 'Content\PageController@edit');
            $this->put('/pages/{id}', 'Content\PageController@update');
            $this->delete('/pages/{id}', 'Content\PageController@destroy');
            
            // Blog
            $this->get('/blog', 'Content\BlogController@index');
            $this->get('/blog/create', 'Content\BlogController@create');
            $this->post('/blog', 'Content\BlogController@store');
            $this->get('/blog/{id}/edit', 'Content\BlogController@edit');
            $this->put('/blog/{id}', 'Content\BlogController@update');
            $this->delete('/blog/{id}', 'Content\BlogController@destroy');
            
            // Menu
            $this->get('/menu', 'Content\MenuController@index');
            $this->post('/menu', 'Content\MenuController@store');
            $this->put('/menu/{id}', 'Content\MenuController@update');
            $this->delete('/menu/{id}', 'Content\MenuController@destroy');
        });
        
        // Media Management routes
        $this->group(['prefix' => '/media', 'middleware' => ['auth']], function() {
            $this->get('/slider', 'Media\SliderController@index');
            $this->post('/slider', 'Media\SliderController@store');
            $this->put('/slider/{id}', 'Media\SliderController@update');
            $this->delete('/slider/{id}', 'Media\SliderController@destroy');
            
            $this->get('/gallery', 'Media\GalleryController@index');
            $this->post('/gallery', 'Media\GalleryController@store');
            $this->delete('/gallery/{id}', 'Media\GalleryController@destroy');
            
            $this->get('/banners', 'Media\BannerController@index');
            $this->post('/banners', 'Media\BannerController@store');
            $this->put('/banners/{id}', 'Media\BannerController@update');
            $this->delete('/banners/{id}', 'Media\BannerController@destroy');
        });
        
        // System routes
        $this->group(['prefix' => '/system', 'middleware' => ['auth', 'admin']], function() {
            $this->get('/settings', 'System\SettingsController@index');
            $this->post('/settings', 'System\SettingsController@update');
            
            $this->get('/users', 'System\UserController@index');
            $this->get('/users/create', 'System\UserController@create');
            $this->post('/users', 'System\UserController@store');
            $this->get('/users/{id}/edit', 'System\UserController@edit');
            $this->put('/users/{id}', 'System\UserController@update');
            $this->delete('/users/{id}', 'System\UserController@destroy');
            
            $this->get('/logs', 'System\LogController@index');
        });
    }
    
    public function get($path, $handler, $middleware = [])
    {
        $this->addRoute('GET', $path, $handler, $middleware);
    }
    
    public function post($path, $handler, $middleware = [])
    {
        $this->addRoute('POST', $path, $handler, $middleware);
    }
    
    public function put($path, $handler, $middleware = [])
    {
        $this->addRoute('PUT', $path, $handler, $middleware);
    }
    
    public function delete($path, $handler, $middleware = [])
    {
        $this->addRoute('DELETE', $path, $handler, $middleware);
    }
    
    public function group($attributes, $callback)
    {
        $prefix = $attributes['prefix'] ?? '';
        $middleware = $attributes['middleware'] ?? [];
        
        $originalPrefix = $this->currentPrefix ?? '';
        $originalMiddleware = $this->currentMiddleware ?? [];
        
        $this->currentPrefix = $originalPrefix . $prefix;
        $this->currentMiddleware = array_merge($originalMiddleware, $middleware);
        
        $callback();
        
        $this->currentPrefix = $originalPrefix;
        $this->currentMiddleware = $originalMiddleware;
    }
    
    private function addRoute($method, $path, $handler, $middleware = [])
    {
        $prefix = $this->currentPrefix ?? '';
        $groupMiddleware = $this->currentMiddleware ?? [];
        
        $fullPath = $prefix . $path;
        $allMiddleware = array_merge($groupMiddleware, $middleware);
        
        $this->routes[] = [
            'method' => $method,
            'path' => $fullPath,
            'handler' => $handler,
            'middleware' => $allMiddleware,
        ];
    }
    
    public function dispatch()
    {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        
        // Remove admin prefix if exists
        $path = preg_replace('#^/admin#', '', $path);
        if (empty($path)) {
            $path = '/';
        }
        
        $route = $this->findRoute($method, $path);
        
        if (!$route) {
            $this->handleNotFound();
            return;
        }
        
        $this->currentRoute = $route;
        
        // Run middleware
        if (!$this->runMiddleware($route['middleware'])) {
            return;
        }
        
        // Execute controller action
        $this->executeHandler($route['handler'], $route['params'] ?? []);
    }
    
    private function findRoute($method, $path)
    {
        foreach ($this->routes as $route) {
            if ($route['method'] !== $method) {
                continue;
            }
            
            $pattern = $this->convertToRegex($route['path']);
            if (preg_match($pattern, $path, $matches)) {
                $route['params'] = $this->extractParams($route['path'], $matches);
                return $route;
            }
        }
        
        return null;
    }
    
    private function convertToRegex($path)
    {
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $path);
        return '#^' . $pattern . '$#';
    }
    
    private function extractParams($routePath, $matches)
    {
        $params = [];
        preg_match_all('/\{([^}]+)\}/', $routePath, $paramNames);
        
        for ($i = 1; $i < count($matches); $i++) {
            $paramName = $paramNames[1][$i - 1];
            $params[$paramName] = $matches[$i];
        }
        
        return $params;
    }
    
    private function runMiddleware($middlewares)
    {
        foreach ($middlewares as $middleware) {
            if (!$this->executeMiddleware($middleware)) {
                return false;
            }
        }
        return true;
    }
    
    private function executeMiddleware($middleware)
    {
        switch ($middleware) {
            case 'auth':
                return $this->authMiddleware();
            case 'admin':
                return $this->adminMiddleware();
            default:
                return true;
        }
    }
    
    private function authMiddleware()
    {
        $session = Application::getInstance()->getSession();
        
        if (!$session->isLoggedIn()) {
            $this->redirect('/login');
            return false;
        }
        
        if ($session->isExpired()) {
            $session->logout();
            $session->flash('error', 'Oturumunuz sona erdi. Lütfen tekrar giriş yapın.');
            $this->redirect('/login');
            return false;
        }
        
        return true;
    }
    
    private function adminMiddleware()
    {
        $session = Application::getInstance()->getSession();
        
        if (!$session->hasRole('admin') && !$session->hasRole('super_admin')) {
            $this->handleForbidden();
            return false;
        }
        
        return true;
    }
    
    private function executeHandler($handler, $params = [])
    {
        list($controllerName, $method) = explode('@', $handler);
        
        $controllerClass = "App\\Controllers\\{$controllerName}";
        
        if (!class_exists($controllerClass)) {
            throw new \Exception("Controller {$controllerClass} not found");
        }
        
        $controller = new $controllerClass();
        
        if (!method_exists($controller, $method)) {
            throw new \Exception("Method {$method} not found in {$controllerClass}");
        }
        
        call_user_func_array([$controller, $method], $params);
    }
    
    private function handleNotFound()
    {
        http_response_code(404);
        include __DIR__ . '/../app/views/errors/404.php';
    }
    
    private function handleForbidden()
    {
        http_response_code(403);
        include __DIR__ . '/../app/views/errors/403.php';
    }
    
    public function redirect($path, $code = 302)
    {
        header("Location: /admin{$path}", true, $code);
        exit;
    }
    
    public function getCurrentRoute()
    {
        return $this->currentRoute;
    }
}
