<?php
$currentPath = $_SERVER['REQUEST_URI'];
$user = $session->getUser();

function isActive($path, $currentPath) {
    return strpos($currentPath, $path) !== false ? 'bg-primary-700 text-white' : 'text-gray-300 hover:bg-sidebar-hover hover:text-white';
}

$menuItems = [
    [
        'title' => 'Dashboard',
        'icon' => 'fas fa-tachometer-alt',
        'url' => '/admin/dashboard',
        'permission' => 'dashboard.view'
    ],
    [
        'title' => 'Otel İşlemleri',
        'icon' => 'fas fa-hotel',
        'children' => [
            [
                'title' => 'Odalar',
                'icon' => 'fas fa-bed',
                'url' => '/admin/hotel/rooms',
                'permission' => 'hotels.manage'
            ],
            [
                'title' => 'Oda Kategorileri',
                'icon' => 'fas fa-tags',
                'url' => '/admin/hotel/categories',
                'permission' => 'hotels.manage'
            ],
            [
                'title' => 'Rezervasyonlar',
                'icon' => 'fas fa-calendar-check',
                'url' => '/admin/hotel/reservations',
                'permission' => 'reservations.manage'
            ],
            [
                'title' => 'Müsaitlik Takvimi',
                'icon' => 'fas fa-calendar',
                'url' => '/admin/hotel/availability',
                'permission' => 'hotels.manage'
            ]
        ]
    ],
    [
        'title' => 'İçerik Yönetimi',
        'icon' => 'fas fa-edit',
        'children' => [
            [
                'title' => 'Sayfalar',
                'icon' => 'fas fa-file-alt',
                'url' => '/admin/content/pages',
                'permission' => 'content.manage'
            ],
            [
                'title' => 'Blog',
                'icon' => 'fas fa-blog',
                'url' => '/admin/content/blog',
                'permission' => 'content.manage'
            ],
            [
                'title' => 'Menü Yönetimi',
                'icon' => 'fas fa-bars',
                'url' => '/admin/content/menu',
                'permission' => 'content.manage'
            ]
        ]
    ],
    [
        'title' => 'Medya Yönetimi',
        'icon' => 'fas fa-images',
        'children' => [
            [
                'title' => 'Slider',
                'icon' => 'fas fa-sliders-h',
                'url' => '/admin/media/slider',
                'permission' => 'media.manage'
            ],
            [
                'title' => 'Galeri',
                'icon' => 'fas fa-photo-video',
                'url' => '/admin/media/gallery',
                'permission' => 'media.manage'
            ],
            [
                'title' => 'Banner Yönetimi',
                'icon' => 'fas fa-ad',
                'url' => '/admin/media/banners',
                'permission' => 'media.manage'
            ]
        ]
    ],
    [
        'title' => 'Pazarlama & SEO',
        'icon' => 'fas fa-chart-line',
        'children' => [
            [
                'title' => 'Promosyonlar',
                'icon' => 'fas fa-percentage',
                'url' => '/admin/marketing/promotions',
                'permission' => 'marketing.manage'
            ],
            [
                'title' => 'Kelime Analizi',
                'icon' => 'fas fa-search',
                'url' => '/admin/marketing/keywords',
                'permission' => 'marketing.manage'
            ],
            [
                'title' => 'Sponsorlar',
                'icon' => 'fas fa-handshake',
                'url' => '/admin/marketing/sponsors',
                'permission' => 'marketing.manage'
            ]
        ]
    ],
    [
        'title' => 'Aktiviteler',
        'icon' => 'fas fa-calendar-alt',
        'url' => '/admin/activities',
        'permission' => 'activities.manage'
    ],
    [
        'title' => 'Raporlar',
        'icon' => 'fas fa-chart-bar',
        'children' => [
            [
                'title' => 'Rezervasyon Raporları',
                'icon' => 'fas fa-chart-pie',
                'url' => '/admin/reports/reservations',
                'permission' => 'reports.view'
            ],
            [
                'title' => 'Gelir Raporları',
                'icon' => 'fas fa-money-bill-wave',
                'url' => '/admin/reports/revenue',
                'permission' => 'reports.view'
            ],
            [
                'title' => 'Doluluk Oranları',
                'icon' => 'fas fa-percentage',
                'url' => '/admin/reports/occupancy',
                'permission' => 'reports.view'
            ]
        ]
    ],
    [
        'title' => 'Sistem Ayarları',
        'icon' => 'fas fa-cog',
        'children' => [
            [
                'title' => 'Genel Ayarlar',
                'icon' => 'fas fa-sliders-h',
                'url' => '/admin/system/settings',
                'permission' => 'system.settings'
            ],
            [
                'title' => 'Kullanıcı Yönetimi',
                'icon' => 'fas fa-users',
                'url' => '/admin/system/users',
                'permission' => 'users.manage'
            ],
            [
                'title' => 'Dil Ayarları',
                'icon' => 'fas fa-language',
                'url' => '/admin/system/languages',
                'permission' => 'system.settings'
            ],
            [
                'title' => 'Sistem Logları',
                'icon' => 'fas fa-file-alt',
                'url' => '/admin/system/logs',
                'permission' => 'system.logs'
            ]
        ]
    ]
];

function renderMenuItem($item, $currentPath, $session, $level = 0) {
    // Check permission
    if (isset($item['permission']) && !$session->hasPermission($item['permission'])) {
        return;
    }
    
    $hasChildren = isset($item['children']) && !empty($item['children']);
    $isActive = isset($item['url']) && strpos($currentPath, $item['url']) !== false;
    
    if ($hasChildren) {
        // Parent menu item with children
        $childrenHtml = '';
        foreach ($item['children'] as $child) {
            $childrenHtml .= renderMenuItem($child, $currentPath, $session, $level + 1);
        }
        
        if (empty($childrenHtml)) {
            return; // No accessible children
        }
        
        echo '<li x-data="{ open: ' . ($isActive ? 'true' : 'false') . ' }">';
        echo '<button @click="open = !open" class="group flex w-full items-center gap-x-3 rounded-md p-2 text-left text-sm font-semibold leading-6 text-gray-300 hover:bg-sidebar-hover hover:text-white">';
        echo '<i class="' . $item['icon'] . ' h-6 w-6 shrink-0"></i>';
        echo $item['title'];
        echo '<i class="fas fa-chevron-right ml-auto h-5 w-5 shrink-0 transition-transform" :class="{ \'rotate-90\': open }"></i>';
        echo '</button>';
        echo '<ul x-show="open" x-transition class="mt-1 px-2">';
        echo $childrenHtml;
        echo '</ul>';
        echo '</li>';
    } else {
        // Single menu item
        $activeClass = $isActive ? 'bg-primary-700 text-white' : 'text-gray-300 hover:bg-sidebar-hover hover:text-white';
        $paddingClass = $level > 0 ? 'pl-8' : '';
        
        echo '<li>';
        echo '<a href="' . $item['url'] . '" class="group flex gap-x-3 rounded-md p-2 text-sm font-semibold leading-6 ' . $activeClass . ' ' . $paddingClass . '">';
        echo '<i class="' . $item['icon'] . ' h-6 w-6 shrink-0"></i>';
        echo $item['title'];
        echo '</a>';
        echo '</li>';
    }
}
?>

<!-- Off-canvas menu for mobile -->
<div class="relative z-50 lg:hidden" x-show="sidebarOpen" style="display: none;">
    <div class="fixed inset-0 bg-gray-900/80" x-show="sidebarOpen" x-transition:enter="transition-opacity ease-linear duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="transition-opacity ease-linear duration-300" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"></div>

    <div class="fixed inset-0 flex">
        <div class="relative mr-16 flex w-full max-w-xs flex-1" x-show="sidebarOpen" x-transition:enter="transition ease-in-out duration-300 transform" x-transition:enter-start="-translate-x-full" x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in-out duration-300 transform" x-transition:leave-start="translate-x-0" x-transition:leave-end="-translate-x-full">
            <div class="absolute left-full top-0 flex w-16 justify-center pt-5">
                <button type="button" class="-m-2.5 p-2.5" @click="sidebarOpen = false">
                    <span class="sr-only">Close sidebar</span>
                    <i class="fas fa-times h-6 w-6 text-white"></i>
                </button>
            </div>

            <div class="flex grow flex-col gap-y-5 overflow-y-auto bg-sidebar-bg px-6 pb-4">
                <div class="flex h-16 shrink-0 items-center">
                    <img class="h-8 w-auto" src="/assets/images/logo.png" alt="Otel Yönetim Sistemi">
                    <span class="ml-3 text-white font-semibold text-lg">Admin Panel</span>
                </div>
                <nav class="flex flex-1 flex-col">
                    <ul role="list" class="flex flex-1 flex-col gap-y-7">
                        <li>
                            <ul role="list" class="-mx-2 space-y-1">
                                <?php
                                foreach ($menuItems as $item) {
                                    renderMenuItem($item, $currentPath, $session);
                                }
                                ?>
                            </ul>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Static sidebar for desktop -->
<div class="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
    <div class="flex grow flex-col gap-y-5 overflow-y-auto bg-sidebar-bg px-6 pb-4">
        <div class="flex h-16 shrink-0 items-center">
            <img class="h-8 w-auto" src="/assets/images/logo.png" alt="Otel Yönetim Sistemi">
            <span class="ml-3 text-white font-semibold text-lg">Admin Panel</span>
        </div>
        <nav class="flex flex-1 flex-col">
            <ul role="list" class="flex flex-1 flex-col gap-y-7">
                <li>
                    <ul role="list" class="-mx-2 space-y-1">
                        <?php
                        foreach ($menuItems as $item) {
                            renderMenuItem($item, $currentPath, $session);
                        }
                        ?>
                    </ul>
                </li>
                
                <!-- User info at bottom -->
                <li class="mt-auto">
                    <div class="flex items-center gap-x-4 px-2 py-3 text-sm font-semibold leading-6 text-gray-300">
                        <div class="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <div class="text-white"><?= htmlspecialchars($user['name']) ?></div>
                            <div class="text-xs text-gray-400"><?= htmlspecialchars($user['role']) ?></div>
                        </div>
                    </div>
                </li>
            </ul>
        </nav>
    </div>
</div>
