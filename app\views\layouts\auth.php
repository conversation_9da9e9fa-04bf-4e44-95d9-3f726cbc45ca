<!DOCTYPE html>
<html lang="tr" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?= $session->getCSRFToken() ?>">
    <title><?= $title ?? 'Giriş' ?> - Otel Yönetim <PERSON>mi</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/assets/css/auth.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="h-full">
    <!-- Flash Messages -->
    <?php 
    $flashMessages = $session->getFlashMessages();
    if (!empty($flashMessages)): 
    ?>
    <div class="fixed top-4 right-4 z-50 space-y-2">
        <?php foreach ($flashMessages as $type => $message): ?>
            <?php
            $alertClasses = [
                'success' => 'bg-green-500 text-white',
                'error' => 'bg-red-500 text-white',
                'warning' => 'bg-yellow-500 text-white',
                'info' => 'bg-blue-500 text-white'
            ];
            
            $iconClasses = [
                'success' => 'fas fa-check-circle',
                'error' => 'fas fa-exclamation-circle',
                'warning' => 'fas fa-exclamation-triangle',
                'info' => 'fas fa-info-circle'
            ];
            
            $alertClass = $alertClasses[$type] ?? $alertClasses['info'];
            $iconClass = $iconClasses[$type] ?? $iconClasses['info'];
            ?>
            
            <div class="alert-auto-hide flex items-center p-4 rounded-lg shadow-lg <?= $alertClass ?>" 
                 style="animation: slideInRight 0.3s ease-out;">
                <div class="flex-shrink-0">
                    <i class="<?= $iconClass ?> h-5 w-5"></i>
                </div>
                <div class="ml-3 flex-1">
                    <p class="text-sm font-medium">
                        <?= htmlspecialchars($message) ?>
                    </p>
                </div>
                <div class="ml-4">
                    <button type="button" onclick="this.parentElement.parentElement.remove()" 
                            class="text-white hover:text-gray-200">
                        <i class="fas fa-times h-4 w-4"></i>
                    </button>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>
    
    <!-- Main Content -->
    <main>
        <?= $content ?>
    </main>
    
    <!-- Footer -->
    <footer class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 py-4">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center text-sm text-gray-500">
                <div>
                    © <?= date('Y') ?> Otel Yönetim Sistemi. Tüm hakları saklıdır.
                </div>
                <div class="flex items-center space-x-4">
                    <span>Versiyon 1.0.0</span>
                    <span>|</span>
                    <a href="/help" class="hover:text-gray-700">Yardım</a>
                    <span>|</span>
                    <a href="/privacy" class="hover:text-gray-700">Gizlilik</a>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Scripts -->
    <script>
        // CSRF Token setup
        window.csrfToken = '<?= $session->getCSRFToken() ?>';
        
        // Auto-hide flash messages
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert-auto-hide');
            alerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s, transform 0.5s';
                alert.style.opacity = '0';
                alert.style.transform = 'translateX(100%)';
                setTimeout(() => alert.remove(), 500);
            });
        }, 5000);
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Alt + L for login page
            if (e.altKey && e.key === 'l') {
                window.location.href = '/admin/login';
            }
            
            // Escape to clear form
            if (e.key === 'Escape') {
                const form = document.querySelector('form');
                if (form) {
                    form.reset();
                }
            }
        });
        
        // Form enhancement
        document.addEventListener('DOMContentLoaded', function() {
            // Add loading states to forms
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function() {
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn && !submitBtn.disabled) {
                        const originalText = submitBtn.innerHTML;
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>İşleniyor...';
                        submitBtn.disabled = true;
                        
                        // Re-enable after 10 seconds as fallback
                        setTimeout(() => {
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;
                        }, 10000);
                    }
                });
            });
            
            // Auto-focus first input
            const firstInput = document.querySelector('input[type="email"], input[type="text"]');
            if (firstInput) {
                firstInput.focus();
            }
        });
        
        // Security enhancements
        (function() {
            // Disable right-click in production
            if (window.location.hostname !== 'localhost') {
                document.addEventListener('contextmenu', function(e) {
                    e.preventDefault();
                });
            }
            
            // Detect developer tools
            let devtools = {open: false, orientation: null};
            setInterval(function() {
                if (window.outerHeight - window.innerHeight > 200 || window.outerWidth - window.innerWidth > 200) {
                    if (!devtools.open) {
                        devtools.open = true;
                        console.clear();
                        console.log('%cDurdur!', 'color: red; font-size: 50px; font-weight: bold;');
                        console.log('%cBu bir tarayıcı özelliğidir ve geliştiriciler için tasarlanmıştır.', 'color: red; font-size: 16px;');
                    }
                } else {
                    devtools.open = false;
                }
            }, 500);
        })();
    </script>
    
    <style>
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }
        
        /* Focus styles */
        input:focus, button:focus {
            outline: 2px solid #3b82f6;
            outline-offset: 2px;
        }
        
        /* Loading animation */
        .fa-spin {
            animation: fa-spin 1s infinite linear;
        }
        
        @keyframes fa-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</body>
</html>
