/* Admin Panel Custom Styles */
/* Otel Yönetim <PERSON> */

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

/* Focus styles */
input:focus, 
textarea:focus, 
select:focus, 
button:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Loading animation */
.fa-spin {
    animation: fa-spin 1s infinite linear;
}

@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Sidebar animations */
.sidebar-enter {
    transform: translateX(-100%);
}

.sidebar-enter-active {
    transform: translateX(0);
    transition: transform 0.3s ease-in-out;
}

.sidebar-exit {
    transform: translateX(0);
}

.sidebar-exit-active {
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
}

/* Card hover effects */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Button styles */
.btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
}

.btn-secondary {
    @apply bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
}

.btn-success {
    @apply bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
}

.btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
}

.btn-warning {
    @apply bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
}

/* Form styles */
.form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500;
}

.form-select {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
}

.form-textarea {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500;
}

.form-checkbox {
    @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
}

.form-radio {
    @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300;
}

/* Table styles */
.table {
    @apply min-w-full divide-y divide-gray-200;
}

.table th {
    @apply px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.table tbody tr:nth-child(even) {
    @apply bg-gray-50;
}

.table tbody tr:hover {
    @apply bg-gray-100;
}

/* Badge styles */
.badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-primary {
    @apply bg-blue-100 text-blue-800;
}

.badge-success {
    @apply bg-green-100 text-green-800;
}

.badge-warning {
    @apply bg-yellow-100 text-yellow-800;
}

.badge-danger {
    @apply bg-red-100 text-red-800;
}

.badge-secondary {
    @apply bg-gray-100 text-gray-800;
}

/* Alert styles */
.alert {
    @apply p-4 rounded-md;
}

.alert-success {
    @apply bg-green-50 border border-green-200 text-green-800;
}

.alert-error {
    @apply bg-red-50 border border-red-200 text-red-800;
}

.alert-warning {
    @apply bg-yellow-50 border border-yellow-200 text-yellow-800;
}

.alert-info {
    @apply bg-blue-50 border border-blue-200 text-blue-800;
}

/* Modal styles */
.modal-overlay {
    @apply fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50;
}

.modal-container {
    @apply relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white;
}

/* Dropdown styles */
.dropdown {
    @apply relative inline-block text-left;
}

.dropdown-menu {
    @apply origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none;
}

.dropdown-item {
    @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900;
}

/* Pagination styles */
.pagination {
    @apply flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6;
}

.pagination-info {
    @apply text-sm text-gray-700;
}

.pagination-nav {
    @apply flex flex-1 justify-between sm:hidden;
}

.pagination-nav-desktop {
    @apply hidden sm:flex sm:flex-1 sm:items-center sm:justify-between;
}

/* Breadcrumb styles */
.breadcrumb {
    @apply flex items-center space-x-2 text-sm text-gray-500;
}

.breadcrumb-item {
    @apply hover:text-gray-700;
}

.breadcrumb-separator {
    @apply text-gray-400;
}

/* Progress bar styles */
.progress {
    @apply w-full bg-gray-200 rounded-full h-2;
}

.progress-bar {
    @apply bg-blue-600 h-2 rounded-full transition-all duration-300;
}

/* Tooltip styles */
.tooltip {
    @apply absolute z-50 px-2 py-1 text-sm text-white bg-gray-900 rounded shadow-lg;
}

/* Animation utilities */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.fade-out {
    animation: fadeOut 0.3s ease-in-out;
}

.slide-in-right {
    animation: slideInRight 0.3s ease-in-out;
}

.slide-out-right {
    animation: slideOutRight 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

@keyframes slideOutRight {
    from { transform: translateX(0); }
    to { transform: translateX(100%); }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-break {
        page-break-after: always;
    }
    
    .print-break-inside {
        page-break-inside: avoid;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .dark-mode {
        @apply bg-gray-900 text-white;
    }
    
    .dark-mode .bg-white {
        @apply bg-gray-800;
    }
    
    .dark-mode .text-gray-900 {
        @apply text-gray-100;
    }
    
    .dark-mode .border-gray-200 {
        @apply border-gray-700;
    }
}

/* Responsive utilities */
@media (max-width: 640px) {
    .mobile-hidden {
        display: none;
    }
}

@media (min-width: 641px) {
    .mobile-only {
        display: none;
    }
}
