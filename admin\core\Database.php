<?php

namespace Core;

use PDO;
use PDOException;

/**
 * Database Connection and Query Builder
 * Otel Yönetim <PERSON> - Admin Panel
 */
class Database
{
    private $connection;
    private $config;
    private static $instance = null;
    
    public function __construct($config)
    {
        $this->config = $config;
        $this->connect();
    }
    
    public static function getInstance($config = null)
    {
        if (self::$instance === null) {
            self::$instance = new self($config);
        }
        return self::$instance;
    }
    
    private function connect()
    {
        $connection = $this->config['connections'][$this->config['default']];
        
        $dsn = sprintf(
            '%s:host=%s;port=%s;dbname=%s;charset=%s',
            $connection['driver'],
            $connection['host'],
            $connection['port'],
            $connection['database'],
            $connection['charset']
        );
        
        try {
            $this->connection = new PDO(
                $dsn,
                $connection['username'],
                $connection['password'],
                $connection['options']
            );
        } catch (PDOException $e) {
            throw new \Exception('Database connection failed: ' . $e->getMessage());
        }
    }
    
    public function getConnection()
    {
        return $this->connection;
    }
    
    public function query($sql, $params = [])
    {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            throw new \Exception('Query failed: ' . $e->getMessage());
        }
    }
    
    public function select($sql, $params = [])
    {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    public function selectOne($sql, $params = [])
    {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    public function insert($table, $data)
    {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        
        $this->query($sql, $data);
        return $this->connection->lastInsertId();
    }
    
    public function update($table, $data, $where, $whereParams = [])
    {
        $set = [];
        foreach (array_keys($data) as $column) {
            $set[] = "{$column} = :{$column}";
        }
        $setClause = implode(', ', $set);
        
        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        
        $params = array_merge($data, $whereParams);
        $stmt = $this->query($sql, $params);
        
        return $stmt->rowCount();
    }
    
    public function delete($table, $where, $params = [])
    {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    public function beginTransaction()
    {
        return $this->connection->beginTransaction();
    }
    
    public function commit()
    {
        return $this->connection->commit();
    }
    
    public function rollback()
    {
        return $this->connection->rollback();
    }
    
    public function table($table)
    {
        return new QueryBuilder($this, $table);
    }
    
    public function raw($sql, $params = [])
    {
        return $this->query($sql, $params);
    }
    
    public function lastInsertId()
    {
        return $this->connection->lastInsertId();
    }
    
    public function escape($value)
    {
        return $this->connection->quote($value);
    }
}
