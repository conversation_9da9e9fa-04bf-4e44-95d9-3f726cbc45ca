# Otel Yönetim Sistemi

Modern ve kullanıcı dostu otel yönetim sistemi. PHP ile geliştirilmiş, responsive tasarım ve güçlü admin paneli ile donatılmıştır.

## 🏨 Özellikler

### Admin Panel
- **Dashboard**: Kapsamlı istatistikler ve grafikler
- **Kullanıcı Yönetimi**: Rol tabanlı yetki sistemi
- **Oda Yönetimi**: Oda kategorileri ve oda durumu takibi
- **Rezervasyon Sistemi**: Gelişmiş rezervasyon yönetimi
- **İçerik Yönetimi**: Sayfa ve blog yönetimi
- **Medya Yönetimi**: Slider, galeri ve banner yönetimi
- **Raporlama**: Detaylı gelir ve doluluk raporları
- **Sistem Ayarları**: Kapsamlı konfigürasyon seçenekleri

### Frontend
- **Modern Tasarım**: Responsive ve mobil uyumlu
- **Oda Galerisi**: G<PERSON>rsel zengin oda sunumu
- **Online Rezervasyon**: Kullanıcı dostu rezervasyon formu
- **Blog Sistemi**: SEO uyumlu blog
- **İletişim**: Entegre iletişim formu

### Teknik Özellikler
- **PHP 7.4+** ile geliştirilmiş
- **MySQL** veritabanı desteği
- **MVC Mimarisi** ile temiz kod yapısı
- **Güvenlik**: CSRF koruması, SQL injection koruması
- **SEO Uyumlu**: Meta tag yönetimi ve URL optimizasyonu
- **Responsive**: Tüm cihazlarda mükemmel görünüm

## 📋 Gereksinimler

- PHP 7.4 veya üzeri
- MySQL 5.7 veya üzeri
- Apache/Nginx web sunucusu
- mod_rewrite etkin

## 🚀 Kurulum

### 1. Dosyaları İndirin
```bash
git clone https://github.com/your-repo/hotel-management.git
cd hotel-management
```

### 2. Veritabanını Oluşturun
```sql
-- MySQL'de yeni veritabanı oluşturun
CREATE DATABASE hotel_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- database.sql dosyasını import edin
mysql -u root -p hotel_management < database.sql
```

### 3. Konfigürasyon
```bash
# .env dosyasını oluşturun
cp .env.example .env

# .env dosyasını düzenleyin
nano .env
```

### 4. Dizin İzinleri
```bash
# Storage dizinlerine yazma izni verin
chmod -R 755 storage/
chmod -R 755 public/assets/
```

### 5. Web Sunucusu Konfigürasyonu

#### Apache
```apache
<VirtualHost *:80>
    ServerName hotel.local
    DocumentRoot /path/to/hotel-management/public
    
    <Directory /path/to/hotel-management/public>
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

#### Nginx
```nginx
server {
    listen 80;
    server_name hotel.local;
    root /path/to/hotel-management/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

## 🔐 Varsayılan Giriş Bilgileri

### Admin Panel (/admin)
- **Super Admin**: <EMAIL> / admin123
- **Manager**: <EMAIL> / manager123  
- **Staff**: <EMAIL> / staff123

## 📁 Proje Yapısı

```
hotel-management/
├── admin/                  # Admin panel sayfaları
├── app/                    # Uygulama dosyaları
│   ├── controllers/        # Controller sınıfları
│   ├── models/            # Model sınıfları
│   ├── views/             # View dosyaları
│   └── helpers/           # Yardımcı sınıflar
├── config/                # Konfigürasyon dosyaları
├── core/                  # Çekirdek sistem dosyaları
├── frontend/              # Frontend sayfaları
├── public/                # Genel erişilebilir dosyalar
│   ├── assets/           # CSS, JS, resim dosyaları
│   └── index.php         # Ana giriş noktası
├── storage/               # Depolama dizinleri
│   ├── logs/             # Log dosyaları
│   ├── cache/            # Önbellek dosyaları
│   └── uploads/          # Yüklenen dosyalar
└── database.sql          # Veritabanı şeması
```

## 🛠️ Geliştirme

### Yeni Controller Ekleme
```php
<?php
namespace App\Controllers\Admin;

use App\Controllers\BaseController;

class YourController extends BaseController
{
    public function index()
    {
        $this->view('admin.your.index', [
            'title' => 'Your Page'
        ]);
    }
}
```

### Yeni Model Ekleme
```php
<?php
namespace App\Models;

use Core\Application;

class YourModel
{
    private $db;
    
    public function __construct()
    {
        $this->db = Application::getInstance()->getDatabase();
    }
}
```

### Yeni Route Ekleme
Router sınıfında `loadRoutes()` metoduna ekleyin:
```php
$this->get('/admin/your-route', 'Admin\YourController@index');
```

## 🔧 Konfigürasyon

### Veritabanı Ayarları
`.env` dosyasında veritabanı bilgilerini güncelleyin:
```env
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=hotel_management
DB_USERNAME=root
DB_PASSWORD=your_password
```

### Mail Ayarları
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_password
MAIL_ENCRYPTION=tls
```

## 📊 Özellik Listesi

### ✅ Tamamlanan Özellikler
- [x] Temel MVC yapısı
- [x] Kullanıcı kimlik doğrulama sistemi
- [x] Admin panel layout'u
- [x] Dashboard istatistikleri
- [x] Kullanıcı yönetimi
- [x] Güvenlik özellikleri (CSRF, rate limiting)
- [x] Responsive tasarım

### 🚧 Geliştirme Aşamasında
- [ ] Oda yönetimi modülü
- [ ] Rezervasyon sistemi
- [ ] İçerik yönetimi
- [ ] Medya yönetimi
- [ ] Raporlama sistemi
- [ ] Frontend sayfaları

### 📋 Planlanan Özellikler
- [ ] Email bildirimleri
- [ ] Ödeme entegrasyonu
- [ ] API geliştirme
- [ ] Mobil uygulama desteği
- [ ] Çoklu dil desteği

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Değişikliklerinizi commit edin (`git commit -m 'Add amazing feature'`)
4. Branch'inizi push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 📝 Lisans

Bu proje MIT lisansı altında lisanslanmıştır. Detaylar için `LICENSE` dosyasına bakın.

## 📞 Destek

Herhangi bir sorun yaşarsanız:
- Issue açın: [GitHub Issues](https://github.com/your-repo/hotel-management/issues)
- Email: <EMAIL>

## 🙏 Teşekkürler

- [Tailwind CSS](https://tailwindcss.com/) - CSS framework
- [Font Awesome](https://fontawesome.com/) - İkonlar
- [Chart.js](https://www.chartjs.org/) - Grafikler
- [Alpine.js](https://alpinejs.dev/) - JavaScript framework

---

**Otel Yönetim Sistemi** - Modern otel işletmeciliği için geliştirilmiştir.
