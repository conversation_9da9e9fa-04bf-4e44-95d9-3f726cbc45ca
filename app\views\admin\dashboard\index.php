<div class="p-6">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p class="mt-1 text-sm text-gray-600">
            Ho<PERSON> geldiniz, <?= htmlspecialchars($user['name']) ?>! İşte sisteminizin genel durumu.
        </p>
    </div>

    <!-- Quick Stats Cards -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <?php foreach ($quickStats as $stat): ?>
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-<?= $stat['color'] ?>-500 rounded-md flex items-center justify-center">
                                <i class="<?= $stat['icon'] ?> text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">
                                    <?= $stat['title'] ?>
                                </dt>
                                <dd class="flex items-baseline">
                                    <div class="text-2xl font-semibold text-gray-900">
                                        <?= $stat['value'] ?>
                                    </div>
                                    <div class="ml-2 flex items-baseline text-sm font-semibold <?= $stat['changeType'] === 'increase' ? 'text-green-600' : 'text-red-600' ?>">
                                        <i class="fas fa-arrow-<?= $stat['changeType'] === 'increase' ? 'up' : 'down' ?> text-xs mr-1"></i>
                                        <?= $stat['change'] ?>
                                    </div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- Charts and Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Revenue Chart -->
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Aylık Gelir Trendi</h3>
                <div class="flex space-x-2">
                    <button class="text-sm text-gray-500 hover:text-gray-700">Son 6 Ay</button>
                    <button class="text-sm text-primary-600 hover:text-primary-700 font-medium">Son 12 Ay</button>
                </div>
            </div>
            <div class="h-64">
                <canvas id="revenueChart"></canvas>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Son Aktiviteler</h3>
                <a href="/admin/activities" class="text-sm text-primary-600 hover:text-primary-700">
                    Tümünü Gör
                </a>
            </div>
            <div class="flow-root">
                <ul class="-mb-8">
                    <?php foreach (array_slice($recentActivities, 0, 5) as $index => $activity): ?>
                        <li>
                            <div class="relative pb-8">
                                <?php if ($index < count($recentActivities) - 1): ?>
                                    <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"></span>
                                <?php endif; ?>
                                <div class="relative flex space-x-3">
                                    <div>
                                        <span class="h-8 w-8 rounded-full bg-<?= $activity['status'] === 'success' ? 'green' : ($activity['status'] === 'pending' ? 'yellow' : 'blue') ?>-500 flex items-center justify-center ring-8 ring-white">
                                            <i class="<?= $activity['icon'] ?> text-white text-xs"></i>
                                        </span>
                                    </div>
                                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                        <div>
                                            <p class="text-sm text-gray-500">
                                                <?= htmlspecialchars($activity['title']) ?>
                                            </p>
                                        </div>
                                        <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                            <time datetime="<?= $activity['time'] ?>">
                                                <?= date('H:i', strtotime($activity['time'])) ?>
                                            </time>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                    <?php endforeach; ?>
                    
                    <?php if (empty($recentActivities)): ?>
                        <li class="text-center py-8 text-gray-500">
                            <i class="fas fa-inbox text-4xl mb-2"></i>
                            <p>Henüz aktivite bulunmuyor</p>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </div>

    <!-- Reservations Chart and Room Status -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Weekly Reservations -->
        <div class="lg:col-span-2 bg-white shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Haftalık Rezervasyonlar</h3>
                <select class="text-sm border-gray-300 rounded-md">
                    <option>Bu Hafta</option>
                    <option>Geçen Hafta</option>
                    <option>Son 4 Hafta</option>
                </select>
            </div>
            <div class="h-64">
                <canvas id="reservationsChart"></canvas>
            </div>
        </div>

        <!-- Room Status -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Oda Durumu</h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                        <span class="text-sm text-gray-600">Müsait</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900"><?= $stats['available_rooms'] ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                        <span class="text-sm text-gray-600">Dolu</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900"><?= $stats['total_rooms'] - $stats['available_rooms'] ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
                        <span class="text-sm text-gray-600">Bakımda</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900">0</span>
                </div>
            </div>
            
            <!-- Room Status Chart -->
            <div class="mt-6 h-32">
                <canvas id="roomStatusChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="mt-8 bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Hızlı İşlemler</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a href="/admin/hotel/reservations/create" class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-calendar-plus text-2xl text-primary-600 mb-2"></i>
                <span class="text-sm font-medium text-gray-900">Yeni Rezervasyon</span>
            </a>
            <a href="/admin/hotel/rooms/create" class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-bed text-2xl text-primary-600 mb-2"></i>
                <span class="text-sm font-medium text-gray-900">Yeni Oda</span>
            </a>
            <a href="/admin/content/blog/create" class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-edit text-2xl text-primary-600 mb-2"></i>
                <span class="text-sm font-medium text-gray-900">Blog Yazısı</span>
            </a>
            <a href="/admin/reports" class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fas fa-chart-bar text-2xl text-primary-600 mb-2"></i>
                <span class="text-sm font-medium text-gray-900">Raporlar</span>
            </a>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: <?= json_encode($chartData['revenue']['labels'] ?? []) ?>,
            datasets: [{
                label: 'Gelir (₺)',
                data: <?= json_encode($chartData['revenue']['data'] ?? []) ?>,
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '₺' + value.toLocaleString('tr-TR');
                        }
                    }
                }
            }
        }
    });

    // Reservations Chart
    const reservationsCtx = document.getElementById('reservationsChart').getContext('2d');
    new Chart(reservationsCtx, {
        type: 'bar',
        data: {
            labels: <?= json_encode($chartData['reservations']['labels'] ?? []) ?>,
            datasets: [{
                label: 'Rezervasyonlar',
                data: <?= json_encode($chartData['reservations']['data'] ?? []) ?>,
                backgroundColor: 'rgba(34, 197, 94, 0.8)',
                borderColor: 'rgb(34, 197, 94)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });

    // Room Status Chart
    const roomStatusCtx = document.getElementById('roomStatusChart').getContext('2d');
    new Chart(roomStatusCtx, {
        type: 'doughnut',
        data: {
            labels: ['Müsait', 'Dolu', 'Bakımda'],
            datasets: [{
                data: [<?= $stats['available_rooms'] ?>, <?= $stats['total_rooms'] - $stats['available_rooms'] ?>, 0],
                backgroundColor: [
                    'rgb(34, 197, 94)',
                    'rgb(239, 68, 68)',
                    'rgb(245, 158, 11)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
});
</script>
