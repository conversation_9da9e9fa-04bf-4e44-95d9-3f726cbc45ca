<!DOCTYPE html>
<html lang="tr" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?= $session->getCSRFToken() ?>">
    <title><?= $title ?? 'Admin Panel' ?> - Otel Yönetim Si<PERSON>mi</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/assets/css/admin.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        },
                        sidebar: {
                            bg: '#1f2937',
                            hover: '#374151',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="h-full" x-data="{ sidebarOpen: false }">
    <div class="min-h-full">
        <!-- Sidebar -->
        <?php include __DIR__ . '/../components/admin/sidebar.php'; ?>
        
        <!-- Main content area -->
        <div class="lg:pl-72">
            <!-- Header -->
            <?php include __DIR__ . '/../components/admin/header.php'; ?>
            
            <!-- Main content -->
            <main class="py-6">
                <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <!-- Flash Messages -->
                    <?php include __DIR__ . '/../components/flash-messages.php'; ?>
                    
                    <!-- Page Content -->
                    <div class="bg-white shadow rounded-lg">
                        <?php if (isset($content)): ?>
                            <?= $content ?>
                        <?php else: ?>
                            <!-- Default content will be loaded here -->
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Footer -->
    <?php include __DIR__ . '/../components/admin/footer.php'; ?>
    
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <div class="flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
                <span class="text-gray-700">Yükleniyor...</span>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="/assets/js/admin.js"></script>
    
    <!-- Page specific scripts -->
    <?php if (isset($scripts)): ?>
        <?= $scripts ?>
    <?php endif; ?>
    
    <script>
        // CSRF Token setup for AJAX requests
        window.csrfToken = '<?= $session->getCSRFToken() ?>';
        
        // Setup AJAX defaults
        if (typeof fetch !== 'undefined') {
            const originalFetch = fetch;
            fetch = function(url, options = {}) {
                if (options.method && options.method.toUpperCase() !== 'GET') {
                    options.headers = options.headers || {};
                    options.headers['X-CSRF-TOKEN'] = window.csrfToken;
                }
                return originalFetch(url, options);
            };
        }
        
        // Auto-hide flash messages
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert-auto-hide');
            alerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 500);
            });
        }, 5000);
        
        // Global functions
        window.showLoading = function() {
            document.getElementById('loading-overlay').classList.remove('hidden');
            document.getElementById('loading-overlay').classList.add('flex');
        };
        
        window.hideLoading = function() {
            document.getElementById('loading-overlay').classList.add('hidden');
            document.getElementById('loading-overlay').classList.remove('flex');
        };
        
        // Confirm delete
        window.confirmDelete = function(message = 'Bu işlemi geri alamazsınız. Emin misiniz?') {
            return confirm(message);
        };
        
        // Format numbers
        window.formatNumber = function(num) {
            return new Intl.NumberFormat('tr-TR').format(num);
        };
        
        // Format currency
        window.formatCurrency = function(amount) {
            return new Intl.NumberFormat('tr-TR', {
                style: 'currency',
                currency: 'TRY'
            }).format(amount);
        };
        
        // Format date
        window.formatDate = function(date) {
            return new Intl.DateTimeFormat('tr-TR').format(new Date(date));
        };
        
        // Auto-save functionality
        window.autoSave = function(formSelector, url, interval = 30000) {
            const form = document.querySelector(formSelector);
            if (!form) return;
            
            setInterval(() => {
                const formData = new FormData(form);
                formData.append('auto_save', '1');
                
                fetch(url, {
                    method: 'POST',
                    body: formData
                }).then(response => {
                    if (response.ok) {
                        console.log('Auto-saved successfully');
                    }
                }).catch(error => {
                    console.error('Auto-save failed:', error);
                });
            }, interval);
        };
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl + S for save
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                const saveBtn = document.querySelector('button[type="submit"], .save-btn');
                if (saveBtn) saveBtn.click();
            }
            
            // Ctrl + N for new
            if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                const newBtn = document.querySelector('.new-btn, [href*="create"]');
                if (newBtn) newBtn.click();
            }
            
            // Escape to close modals
            if (e.key === 'Escape') {
                const modals = document.querySelectorAll('.modal, [x-show]');
                modals.forEach(modal => {
                    if (modal.style.display !== 'none') {
                        modal.style.display = 'none';
                    }
                });
            }
        });
        
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            const tooltips = document.querySelectorAll('[data-tooltip]');
            tooltips.forEach(element => {
                element.addEventListener('mouseenter', function() {
                    const tooltip = document.createElement('div');
                    tooltip.className = 'absolute z-50 px-2 py-1 text-sm text-white bg-gray-900 rounded shadow-lg';
                    tooltip.textContent = this.getAttribute('data-tooltip');
                    tooltip.style.top = this.offsetTop - 30 + 'px';
                    tooltip.style.left = this.offsetLeft + 'px';
                    document.body.appendChild(tooltip);
                    
                    this.addEventListener('mouseleave', function() {
                        tooltip.remove();
                    }, { once: true });
                });
            });
        });
    </script>
</body>
</html>
