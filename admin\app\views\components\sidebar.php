<?php
$currentPath = $_SERVER['REQUEST_URI'];
$user = $session->getUser();

function isActive($path, $currentPath) {
    return strpos($currentPath, $path) !== false ? 'bg-primary-700 text-white' : 'text-gray-300 hover:bg-sidebar-hover hover:text-white';
}
?>

<!-- Off-canvas menu for mobile -->
<div class="relative z-50 lg:hidden" x-show="sidebarOpen" style="display: none;">
    <div class="fixed inset-0 bg-gray-900/80" x-show="sidebarOpen" x-transition:enter="transition-opacity ease-linear duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="transition-opacity ease-linear duration-300" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"></div>

    <div class="fixed inset-0 flex">
        <div class="relative mr-16 flex w-full max-w-xs flex-1" x-show="sidebarOpen" x-transition:enter="transition ease-in-out duration-300 transform" x-transition:enter-start="-translate-x-full" x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in-out duration-300 transform" x-transition:leave-start="translate-x-0" x-transition:leave-end="-translate-x-full">
            <div class="absolute left-full top-0 flex w-16 justify-center pt-5">
                <button type="button" class="-m-2.5 p-2.5" @click="sidebarOpen = false">
                    <span class="sr-only">Close sidebar</span>
                    <i class="fas fa-times h-6 w-6 text-white"></i>
                </button>
            </div>

            <div class="flex grow flex-col gap-y-5 overflow-y-auto bg-sidebar-bg px-6 pb-4">
                <div class="flex h-16 shrink-0 items-center">
                    <img class="h-8 w-auto" src="/admin/assets/images/logo.png" alt="Otel Yönetim Sistemi">
                    <span class="ml-3 text-white font-semibold text-lg">Admin Panel</span>
                </div>
                <nav class="flex flex-1 flex-col">
                    <ul role="list" class="flex flex-1 flex-col gap-y-7">
                        <li>
                            <ul role="list" class="-mx-2 space-y-1">
                                <!-- Navigation items will be included here -->
                                <?php include __DIR__ . '/navigation-items.php'; ?>
                            </ul>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Static sidebar for desktop -->
<div class="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
    <div class="flex grow flex-col gap-y-5 overflow-y-auto bg-sidebar-bg px-6 pb-4">
        <div class="flex h-16 shrink-0 items-center">
            <img class="h-8 w-auto" src="/admin/assets/images/logo.png" alt="Otel Yönetim Sistemi">
            <span class="ml-3 text-white font-semibold text-lg">Admin Panel</span>
        </div>
        <nav class="flex flex-1 flex-col">
            <ul role="list" class="flex flex-1 flex-col gap-y-7">
                <li>
                    <ul role="list" class="-mx-2 space-y-1">
                        <!-- Navigation items -->
                        <?php include __DIR__ . '/navigation-items.php'; ?>
                    </ul>
                </li>
                
                <!-- User info at bottom -->
                <li class="mt-auto">
                    <div class="flex items-center gap-x-4 px-2 py-3 text-sm font-semibold leading-6 text-gray-300">
                        <div class="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <div class="text-white"><?= htmlspecialchars($user['name']) ?></div>
                            <div class="text-xs text-gray-400"><?= htmlspecialchars($user['role']) ?></div>
                        </div>
                    </div>
                </li>
            </ul>
        </nav>
    </div>
</div>
