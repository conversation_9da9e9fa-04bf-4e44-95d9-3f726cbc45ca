<?php

namespace Core;

/**
 * Router Class
 * Otel Yönetim <PERSON>
 */
class Router
{
    private $routes = [];
    private $middlewares = [];
    private $currentRoute = null;
    private $currentPrefix = '';
    private $currentMiddleware = [];
    
    public function __construct()
    {
        $this->loadRoutes();
    }
    
    private function loadRoutes()
    {
        // Admin routes
        $this->group(['prefix' => '/admin', 'middleware' => ['auth']], function() {
            // Authentication routes (no auth middleware)
            $this->get('/login', 'Admin\AuthController@showLoginForm', []);
            $this->post('/login', 'Admin\AuthController@login', []);
            $this->post('/logout', 'Admin\AuthController@logout');
            
            // Dashboard
            $this->get('/', 'Admin\DashboardController@index');
            $this->get('/dashboard', 'Admin\DashboardController@index');
            
            // Hotel Management
            $this->group(['prefix' => '/hotel'], function() {
                // Rooms
                $this->get('/rooms', 'Admin\Hotel\RoomController@index');
                $this->get('/rooms/create', 'Admin\Hotel\RoomController@create');
                $this->post('/rooms', 'Admin\Hotel\RoomController@store');
                $this->get('/rooms/{id}', 'Admin\Hotel\RoomController@show');
                $this->get('/rooms/{id}/edit', 'Admin\Hotel\RoomController@edit');
                $this->put('/rooms/{id}', 'Admin\Hotel\RoomController@update');
                $this->delete('/rooms/{id}', 'Admin\Hotel\RoomController@destroy');
                
                // Categories
                $this->get('/categories', 'Admin\Hotel\CategoryController@index');
                $this->post('/categories', 'Admin\Hotel\CategoryController@store');
                $this->put('/categories/{id}', 'Admin\Hotel\CategoryController@update');
                $this->delete('/categories/{id}', 'Admin\Hotel\CategoryController@destroy');
                
                // Reservations
                $this->get('/reservations', 'Admin\Hotel\ReservationController@index');
                $this->get('/reservations/create', 'Admin\Hotel\ReservationController@create');
                $this->post('/reservations', 'Admin\Hotel\ReservationController@store');
                $this->get('/reservations/{id}', 'Admin\Hotel\ReservationController@show');
                $this->put('/reservations/{id}', 'Admin\Hotel\ReservationController@update');
                $this->delete('/reservations/{id}', 'Admin\Hotel\ReservationController@destroy');
            });
            
            // Content Management
            $this->group(['prefix' => '/content'], function() {
                $this->get('/pages', 'Admin\Content\PageController@index');
                $this->get('/pages/create', 'Admin\Content\PageController@create');
                $this->post('/pages', 'Admin\Content\PageController@store');
                $this->get('/pages/{id}/edit', 'Admin\Content\PageController@edit');
                $this->put('/pages/{id}', 'Admin\Content\PageController@update');
                $this->delete('/pages/{id}', 'Admin\Content\PageController@destroy');
                
                $this->get('/blog', 'Admin\Content\BlogController@index');
                $this->get('/blog/create', 'Admin\Content\BlogController@create');
                $this->post('/blog', 'Admin\Content\BlogController@store');
                $this->get('/blog/{id}/edit', 'Admin\Content\BlogController@edit');
                $this->put('/blog/{id}', 'Admin\Content\BlogController@update');
                $this->delete('/blog/{id}', 'Admin\Content\BlogController@destroy');
                
                $this->get('/menu', 'Admin\Content\MenuController@index');
                $this->post('/menu', 'Admin\Content\MenuController@store');
                $this->put('/menu/{id}', 'Admin\Content\MenuController@update');
                $this->delete('/menu/{id}', 'Admin\Content\MenuController@destroy');
            });
            
            // Media Management
            $this->group(['prefix' => '/media'], function() {
                $this->get('/slider', 'Admin\Media\SliderController@index');
                $this->post('/slider', 'Admin\Media\SliderController@store');
                $this->put('/slider/{id}', 'Admin\Media\SliderController@update');
                $this->delete('/slider/{id}', 'Admin\Media\SliderController@destroy');
                
                $this->get('/gallery', 'Admin\Media\GalleryController@index');
                $this->post('/gallery', 'Admin\Media\GalleryController@store');
                $this->delete('/gallery/{id}', 'Admin\Media\GalleryController@destroy');
                
                $this->get('/banners', 'Admin\Media\BannerController@index');
                $this->post('/banners', 'Admin\Media\BannerController@store');
                $this->put('/banners/{id}', 'Admin\Media\BannerController@update');
                $this->delete('/banners/{id}', 'Admin\Media\BannerController@destroy');
            });
            
            // System Management
            $this->group(['prefix' => '/system', 'middleware' => ['admin']], function() {
                $this->get('/settings', 'Admin\System\SettingsController@index');
                $this->post('/settings', 'Admin\System\SettingsController@update');
                
                $this->get('/users', 'Admin\System\UserController@index');
                $this->get('/users/create', 'Admin\System\UserController@create');
                $this->post('/users', 'Admin\System\UserController@store');
                $this->get('/users/{id}/edit', 'Admin\System\UserController@edit');
                $this->put('/users/{id}', 'Admin\System\UserController@update');
                $this->delete('/users/{id}', 'Admin\System\UserController@destroy');
                
                $this->get('/logs', 'Admin\System\LogController@index');
            });
        });
        
        // Frontend routes
        $this->get('/', 'Frontend\HomeController@index');
        $this->get('/rooms', 'Frontend\RoomController@index');
        $this->get('/rooms/{id}', 'Frontend\RoomController@show');
        $this->get('/reservations', 'Frontend\ReservationController@index');
        $this->post('/reservations', 'Frontend\ReservationController@store');
        $this->get('/gallery', 'Frontend\GalleryController@index');
        $this->get('/blog', 'Frontend\BlogController@index');
        $this->get('/blog/{slug}', 'Frontend\BlogController@show');
        $this->get('/contact', 'Frontend\ContactController@index');
        $this->post('/contact', 'Frontend\ContactController@store');
        $this->get('/about', 'Frontend\AboutController@index');
    }
    
    public function get($path, $handler, $middleware = [])
    {
        $this->addRoute('GET', $path, $handler, $middleware);
    }
    
    public function post($path, $handler, $middleware = [])
    {
        $this->addRoute('POST', $path, $handler, $middleware);
    }
    
    public function put($path, $handler, $middleware = [])
    {
        $this->addRoute('PUT', $path, $handler, $middleware);
    }
    
    public function delete($path, $handler, $middleware = [])
    {
        $this->addRoute('DELETE', $path, $handler, $middleware);
    }
    
    public function group($attributes, $callback)
    {
        $prefix = $attributes['prefix'] ?? '';
        $middleware = $attributes['middleware'] ?? [];
        
        $originalPrefix = $this->currentPrefix;
        $originalMiddleware = $this->currentMiddleware;
        
        $this->currentPrefix = $originalPrefix . $prefix;
        $this->currentMiddleware = array_merge($originalMiddleware, $middleware);
        
        $callback();
        
        $this->currentPrefix = $originalPrefix;
        $this->currentMiddleware = $originalMiddleware;
    }
    
    private function addRoute($method, $path, $handler, $middleware = [])
    {
        $fullPath = $this->currentPrefix . $path;
        $allMiddleware = array_merge($this->currentMiddleware, $middleware);
        
        $this->routes[] = [
            'method' => $method,
            'path' => $fullPath,
            'handler' => $handler,
            'middleware' => $allMiddleware,
        ];
    }
    
    public function dispatch()
    {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        
        // Handle method override for PUT/DELETE
        if ($method === 'POST' && isset($_POST['_method'])) {
            $method = strtoupper($_POST['_method']);
        }
        
        $route = $this->findRoute($method, $path);
        
        if (!$route) {
            $this->handleNotFound();
            return;
        }
        
        $this->currentRoute = $route;
        
        // Run middleware
        if (!$this->runMiddleware($route['middleware'])) {
            return;
        }
        
        // Execute controller action
        $this->executeHandler($route['handler'], $route['params'] ?? []);
    }
    
    private function findRoute($method, $path)
    {
        foreach ($this->routes as $route) {
            if ($route['method'] !== $method) {
                continue;
            }
            
            $pattern = $this->convertToRegex($route['path']);
            if (preg_match($pattern, $path, $matches)) {
                $route['params'] = $this->extractParams($route['path'], $matches);
                return $route;
            }
        }
        
        return null;
    }
    
    private function convertToRegex($path)
    {
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $path);
        return '#^' . $pattern . '$#';
    }
    
    private function extractParams($routePath, $matches)
    {
        $params = [];
        preg_match_all('/\{([^}]+)\}/', $routePath, $paramNames);
        
        for ($i = 1; $i < count($matches); $i++) {
            $paramName = $paramNames[1][$i - 1];
            $params[$paramName] = $matches[$i];
        }
        
        return $params;
    }
    
    private function runMiddleware($middlewares)
    {
        foreach ($middlewares as $middleware) {
            if (!$this->executeMiddleware($middleware)) {
                return false;
            }
        }
        return true;
    }
    
    private function executeMiddleware($middleware)
    {
        switch ($middleware) {
            case 'auth':
                return $this->authMiddleware();
            case 'admin':
                return $this->adminMiddleware();
            default:
                return true;
        }
    }
    
    private function authMiddleware()
    {
        $session = Application::getInstance()->getSession();
        
        if (!$session->isLoggedIn()) {
            $this->redirect('/admin/login');
            return false;
        }
        
        if ($session->isExpired()) {
            $session->logout();
            $session->flash('error', 'Oturumunuz sona erdi. Lütfen tekrar giriş yapın.');
            $this->redirect('/admin/login');
            return false;
        }
        
        return true;
    }
    
    private function adminMiddleware()
    {
        $session = Application::getInstance()->getSession();
        
        if (!$session->hasRole('admin') && !$session->hasRole('super_admin')) {
            $this->handleForbidden();
            return false;
        }
        
        return true;
    }
    
    private function executeHandler($handler, $params = [])
    {
        list($controllerName, $method) = explode('@', $handler);
        
        $controllerClass = "App\\Controllers\\{$controllerName}";
        
        if (!class_exists($controllerClass)) {
            throw new \Exception("Controller {$controllerClass} not found");
        }
        
        $controller = new $controllerClass();
        
        if (!method_exists($controller, $method)) {
            throw new \Exception("Method {$method} not found in {$controllerClass}");
        }
        
        call_user_func_array([$controller, $method], $params);
    }
    
    private function handleNotFound()
    {
        http_response_code(404);
        include __DIR__ . '/../app/views/errors/404.php';
    }
    
    private function handleForbidden()
    {
        http_response_code(403);
        include __DIR__ . '/../app/views/errors/403.php';
    }
    
    public function redirect($path, $code = 302)
    {
        header("Location: {$path}", true, $code);
        exit;
    }
    
    public function getCurrentRoute()
    {
        return $this->currentRoute;
    }
}
