<?php

/**
 * Autoloader Class
 * Otel Yönetim <PERSON> - Admin Panel
 */
class Autoloader
{
    private static $instance = null;
    private $namespaces = [];
    private $classMap = [];
    
    private function __construct()
    {
        $this->registerNamespaces();
    }
    
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public static function register()
    {
        $instance = self::getInstance();
        spl_autoload_register([$instance, 'loadClass']);
    }
    
    private function registerNamespaces()
    {
        $basePath = __DIR__ . '/..';
        
        $this->namespaces = [
            'Core\\' => $basePath . '/core/',
            'App\\Controllers\\' => $basePath . '/app/controllers/',
            'App\\Models\\' => $basePath . '/app/models/',
            'App\\Middleware\\' => $basePath . '/app/middleware/',
            'App\\Helpers\\' => $basePath . '/app/helpers/',
        ];
    }
    
    public function addNamespace($namespace, $path)
    {
        $namespace = rtrim($namespace, '\\') . '\\';
        $path = rtrim($path, '/') . '/';
        $this->namespaces[$namespace] = $path;
    }
    
    public function addClassMap(array $classMap)
    {
        $this->classMap = array_merge($this->classMap, $classMap);
    }
    
    public function loadClass($className)
    {
        // Check class map first
        if (isset($this->classMap[$className])) {
            $file = $this->classMap[$className];
            if (file_exists($file)) {
                require_once $file;
                return true;
            }
        }
        
        // Try namespace mapping
        foreach ($this->namespaces as $namespace => $path) {
            if (strpos($className, $namespace) === 0) {
                $relativeClass = substr($className, strlen($namespace));
                $file = $path . str_replace('\\', '/', $relativeClass) . '.php';
                
                if (file_exists($file)) {
                    require_once $file;
                    return true;
                }
            }
        }
        
        // Try PSR-4 style loading
        $file = $this->convertClassNameToPath($className);
        if (file_exists($file)) {
            require_once $file;
            return true;
        }
        
        return false;
    }
    
    private function convertClassNameToPath($className)
    {
        $basePath = __DIR__ . '/..';
        
        // Convert namespace separators to directory separators
        $path = str_replace('\\', '/', $className);
        
        // Handle different namespace patterns
        if (strpos($path, 'App/Controllers/') === 0) {
            $path = str_replace('App/Controllers/', 'app/controllers/', $path);
        } elseif (strpos($path, 'App/Models/') === 0) {
            $path = str_replace('App/Models/', 'app/models/', $path);
        } elseif (strpos($path, 'Core/') === 0) {
            $path = str_replace('Core/', 'core/', $path);
        }
        
        return $basePath . '/' . $path . '.php';
    }
    
    public function getLoadedClasses()
    {
        return get_declared_classes();
    }
    
    public function getNamespaces()
    {
        return $this->namespaces;
    }
    
    public function getClassMap()
    {
        return $this->classMap;
    }
}
