<!-- Footer -->
<footer class="bg-white border-t border-gray-200 lg:ml-72">
    <div class="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row justify-between items-center">
            <div class="flex items-center space-x-4">
                <p class="text-sm text-gray-500">
                    © <?= date('Y') ?> Otel Yönetim Sistemi. Tüm hakları saklıdır.
                </p>
                <span class="text-gray-300">|</span>
                <p class="text-sm text-gray-500">
                    Versiyon 1.0.0
                </p>
            </div>
            
            <div class="flex items-center space-x-6 mt-4 md:mt-0">
                <!-- System Status -->
                <div class="flex items-center space-x-2">
                    <div class="h-2 w-2 bg-green-500 rounded-full"></div>
                    <span class="text-sm text-gray-500">Sistem Aktif</span>
                </div>
                
                <!-- Quick Links -->
                <div class="flex items-center space-x-4">
                    <a href="/admin/help" class="text-sm text-gray-500 hover:text-gray-700">
                        <i class="fas fa-question-circle mr-1"></i>
                        Yardım
                    </a>
                    <a href="/admin/support" class="text-sm text-gray-500 hover:text-gray-700">
                        <i class="fas fa-life-ring mr-1"></i>
                        Destek
                    </a>
                    <a href="/admin/documentation" class="text-sm text-gray-500 hover:text-gray-700">
                        <i class="fas fa-book mr-1"></i>
                        Dokümantasyon
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Additional Footer Info -->
        <div class="mt-4 pt-4 border-t border-gray-100">
            <div class="flex flex-col md:flex-row justify-between items-center text-xs text-gray-400">
                <div class="flex items-center space-x-4">
                    <span>Son Giriş: <?= date('d.m.Y H:i', $session->get('login_time')) ?></span>
                    <span>|</span>
                    <span>IP: <?= $_SERVER['REMOTE_ADDR'] ?? 'Bilinmiyor' ?></span>
                    <span>|</span>
                    <span>Tarayıcı: <?= substr($_SERVER['HTTP_USER_AGENT'] ?? 'Bilinmiyor', 0, 50) ?>...</span>
                </div>
                
                <div class="flex items-center space-x-2 mt-2 md:mt-0">
                    <span>Powered by</span>
                    <strong>Hotel Management System</strong>
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- Back to Top Button -->
<button id="back-to-top" class="fixed bottom-8 right-8 bg-primary-600 hover:bg-primary-700 text-white p-3 rounded-full shadow-lg transition-all duration-300 opacity-0 invisible z-50">
    <i class="fas fa-arrow-up"></i>
</button>

<script>
// Back to top functionality
window.addEventListener('scroll', function() {
    const backToTop = document.getElementById('back-to-top');
    if (window.pageYOffset > 300) {
        backToTop.classList.remove('opacity-0', 'invisible');
        backToTop.classList.add('opacity-100', 'visible');
    } else {
        backToTop.classList.add('opacity-0', 'invisible');
        backToTop.classList.remove('opacity-100', 'visible');
    }
});

document.getElementById('back-to-top').addEventListener('click', function() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
});
</script>
