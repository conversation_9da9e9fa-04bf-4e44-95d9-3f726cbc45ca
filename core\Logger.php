<?php

namespace Core;

/**
 * Logger Class
 * Otel Yönetim Si<PERSON>mi
 */
class Logger
{
    private $config;
    private $channel;
    
    public function __construct($channel = 'default')
    {
        $this->channel = $channel;
        $this->config = Application::getInstance()->getConfig('app.logging');
    }
    
    public function emergency($message, array $context = [])
    {
        $this->log('emergency', $message, $context);
    }
    
    public function alert($message, array $context = [])
    {
        $this->log('alert', $message, $context);
    }
    
    public function critical($message, array $context = [])
    {
        $this->log('critical', $message, $context);
    }
    
    public function error($message, array $context = [])
    {
        $this->log('error', $message, $context);
    }
    
    public function warning($message, array $context = [])
    {
        $this->log('warning', $message, $context);
    }
    
    public function notice($message, array $context = [])
    {
        $this->log('notice', $message, $context);
    }
    
    public function info($message, array $context = [])
    {
        $this->log('info', $message, $context);
    }
    
    public function debug($message, array $context = [])
    {
        $this->log('debug', $message, $context);
    }
    
    public function log($level, $message, array $context = [])
    {
        $channelConfig = $this->config['channels'][$this->channel] ?? $this->config['channels']['daily'];
        
        if (!$this->shouldLog($level, $channelConfig['level'])) {
            return;
        }
        
        $logEntry = $this->formatLogEntry($level, $message, $context);
        
        switch ($channelConfig['driver']) {
            case 'daily':
                $this->writeDailyLog($logEntry, $channelConfig);
                break;
            case 'single':
                $this->writeSingleLog($logEntry, $channelConfig);
                break;
        }
    }
    
    private function shouldLog($level, $configLevel)
    {
        $levels = [
            'debug' => 0,
            'info' => 1,
            'notice' => 2,
            'warning' => 3,
            'error' => 4,
            'critical' => 5,
            'alert' => 6,
            'emergency' => 7,
        ];
        
        return $levels[$level] >= $levels[$configLevel];
    }
    
    private function formatLogEntry($level, $message, array $context = [])
    {
        $timestamp = date('Y-m-d H:i:s');
        $levelUpper = strtoupper($level);
        
        $entry = "[{$timestamp}] {$levelUpper}: {$message}";
        
        if (!empty($context)) {
            $entry .= ' ' . json_encode($context, JSON_UNESCAPED_UNICODE);
        }
        
        // Add request information
        if (isset($_SERVER['REQUEST_METHOD'])) {
            $entry .= " | {$_SERVER['REQUEST_METHOD']} {$_SERVER['REQUEST_URI']}";
        }
        
        // Add user information if available
        try {
            $session = Application::getInstance()->getSession();
            if ($session->isLoggedIn()) {
                $user = $session->getUser();
                $entry .= " | User: {$user['id']} ({$user['email']})";
            }
        } catch (\Exception $e) {
            // Ignore session errors during logging
        }
        
        // Add IP address
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $entry .= " | IP: {$ip}";
        
        return $entry . PHP_EOL;
    }
    
    private function writeDailyLog($logEntry, $config)
    {
        $date = date('Y-m-d');
        $filename = str_replace('.log', "-{$date}.log", basename($config['path']));
        $logPath = dirname($config['path']) . '/' . $filename;
        
        $this->ensureDirectoryExists(dirname($logPath));
        
        file_put_contents($logPath, $logEntry, FILE_APPEND | LOCK_EX);
        
        // Clean old log files
        if (isset($config['days'])) {
            $this->cleanOldLogs(dirname($logPath), $config['days']);
        }
    }
    
    private function writeSingleLog($logEntry, $config)
    {
        $this->ensureDirectoryExists(dirname($config['path']));
        file_put_contents($config['path'], $logEntry, FILE_APPEND | LOCK_EX);
    }
    
    private function ensureDirectoryExists($directory)
    {
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
    }
    
    private function cleanOldLogs($directory, $days)
    {
        $files = glob($directory . '/*.log');
        $cutoff = time() - ($days * 24 * 60 * 60);
        
        foreach ($files as $file) {
            if (filemtime($file) < $cutoff) {
                unlink($file);
            }
        }
    }
    
    public static function security($message, array $context = [])
    {
        $logger = new self('security');
        $logger->info($message, $context);
    }
    
    public static function loginAttempt($email, $success, $ip = null)
    {
        $ip = $ip ?: ($_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown');
        $status = $success ? 'SUCCESS' : 'FAILED';
        
        self::security("Login attempt: {$status} for {$email}", [
            'email' => $email,
            'success' => $success,
            'ip' => $ip,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    }
    
    public static function userAction($action, $details = [])
    {
        try {
            $session = Application::getInstance()->getSession();
            $user = $session->getUser();
            
            if ($user) {
                $logger = new self('security');
                $logger->info("User action: {$action}", array_merge([
                    'user_id' => $user['id'],
                    'user_email' => $user['email'],
                    'action' => $action
                ], $details));
            }
        } catch (\Exception $e) {
            // Ignore errors during logging
        }
    }
}
