<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use Core\Logger;

/**
 * Admin Authentication Controller
 * Otel Yönetim Si<PERSON>mi
 */
class AuthController extends BaseController
{
    /**
     * Show login form
     */
    public function showLoginForm()
    {
        // If already logged in, redirect to dashboard
        if ($this->session->isLoggedIn()) {
            $this->redirect('/admin/dashboard');
        }
        
        $this->view('admin.auth.login', [
            'title' => 'Admin Girişi'
        ], 'layouts.auth');
    }
    
    /**
     * Handle login attempt
     */
    public function login()
    {
        $email = $_POST['email'] ?? '';
        $password = $_POST['password'] ?? '';
        $remember = isset($_POST['remember']);
        
        // Validate input
        if (!$this->validate($_POST, [
            'email' => 'required|email',
            'password' => 'required|min:6'
        ])) {
            return $this->back($_POST);
        }
        
        // Check rate limiting
        if ($this->isRateLimited($email)) {
            $this->session->flash('error', 'Çok fazla başarısız giriş denemesi. Lütfen 15 dakika sonra tekrar deneyin.');
            return $this->back();
        }
        
        // Attempt authentication
        $user = $this->authenticateUser($email, $password);
        
        if ($user) {
            // Successful login
            $this->session->login($user);
            $this->clearLoginAttempts($email);
            
            // Log successful login
            Logger::loginAttempt($email, true);
            Logger::userAction('login', ['ip' => $_SERVER['REMOTE_ADDR']]);
            
            $this->session->flash('success', 'Hoş geldiniz, ' . $user['name']);
            
            // Redirect to intended page or dashboard
            $intended = $this->session->get('intended_url', '/admin/dashboard');
            $this->session->remove('intended_url');
            $this->redirect($intended);
            
        } else {
            // Failed login
            $this->recordLoginAttempt($email);
            Logger::loginAttempt($email, false);
            
            $this->session->flash('error', 'Email veya şifre hatalı.');
            return $this->back(['email' => $email]);
        }
    }
    
    /**
     * Handle logout
     */
    public function logout()
    {
        $user = $this->session->getUser();
        
        if ($user) {
            Logger::userAction('logout');
        }
        
        $this->session->logout();
        $this->session->flash('success', 'Başarıyla çıkış yaptınız.');
        $this->redirect('/admin/login');
    }
    
    /**
     * Authenticate user credentials
     */
    private function authenticateUser($email, $password)
    {
        $user = $this->db->selectOne(
            "SELECT * FROM users WHERE email = :email AND status = 'active'",
            ['email' => $email]
        );
        
        if ($user && password_verify($password, $user['password'])) {
            // Update last login
            $this->db->update('users', [
                'last_login' => date('Y-m-d H:i:s'),
                'last_login_ip' => $_SERVER['REMOTE_ADDR'] ?? null
            ], 'id = :id', ['id' => $user['id']]);
            
            return $user;
        }
        
        return false;
    }
    
    /**
     * Check if IP is rate limited
     */
    private function isRateLimited($email)
    {
        $maxAttempts = $this->config['app']['security']['max_login_attempts'];
        $lockoutDuration = $this->config['app']['security']['lockout_duration'] * 60; // Convert to seconds
        
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $since = date('Y-m-d H:i:s', time() - $lockoutDuration);
        
        // Check attempts by IP
        $ipAttempts = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM login_attempts 
             WHERE ip = :ip AND created_at > :since",
            ['ip' => $ip, 'since' => $since]
        );
        
        // Check attempts by email
        $emailAttempts = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM login_attempts 
             WHERE email = :email AND created_at > :since",
            ['email' => $email, 'since' => $since]
        );
        
        return ($ipAttempts['count'] >= $maxAttempts) || ($emailAttempts['count'] >= $maxAttempts);
    }
    
    /**
     * Record failed login attempt
     */
    private function recordLoginAttempt($email)
    {
        $this->db->insert('login_attempts', [
            'email' => $email,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * Clear login attempts for successful login
     */
    private function clearLoginAttempts($email)
    {
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        
        $this->db->delete('login_attempts', 
            'email = :email OR ip = :ip', 
            ['email' => $email, 'ip' => $ip]
        );
    }
    
    /**
     * Show forgot password form
     */
    public function showForgotPasswordForm()
    {
        $this->view('admin.auth.forgot-password', [
            'title' => 'Şifremi Unuttum'
        ], 'layouts.auth');
    }
    
    /**
     * Handle forgot password request
     */
    public function forgotPassword()
    {
        $email = $_POST['email'] ?? '';
        
        if (!$this->validate($_POST, ['email' => 'required|email'])) {
            return $this->back($_POST);
        }
        
        $user = $this->db->selectOne(
            "SELECT * FROM users WHERE email = :email AND status = 'active'",
            ['email' => $email]
        );
        
        if ($user) {
            // Generate reset token
            $token = bin2hex(random_bytes(32));
            $expires = date('Y-m-d H:i:s', time() + 3600); // 1 hour
            
            // Save reset token
            $this->db->insert('password_resets', [
                'email' => $email,
                'token' => $token,
                'expires_at' => $expires,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            // TODO: Send email with reset link
            // For now, just show success message
            Logger::userAction('password_reset_requested', ['email' => $email]);
        }
        
        // Always show success message for security
        $this->session->flash('success', 'Şifre sıfırlama bağlantısı email adresinize gönderildi.');
        $this->redirect('/admin/login');
    }
    
    /**
     * Show reset password form
     */
    public function showResetPasswordForm($token)
    {
        $reset = $this->db->selectOne(
            "SELECT * FROM password_resets WHERE token = :token AND expires_at > NOW()",
            ['token' => $token]
        );
        
        if (!$reset) {
            $this->session->flash('error', 'Geçersiz veya süresi dolmuş şifre sıfırlama bağlantısı.');
            $this->redirect('/admin/login');
        }
        
        $this->view('admin.auth.reset-password', [
            'title' => 'Şifre Sıfırla',
            'token' => $token
        ], 'layouts.auth');
    }
    
    /**
     * Handle password reset
     */
    public function resetPassword()
    {
        $token = $_POST['token'] ?? '';
        $password = $_POST['password'] ?? '';
        $passwordConfirmation = $_POST['password_confirmation'] ?? '';
        
        if (!$this->validate($_POST, [
            'token' => 'required',
            'password' => 'required|min:8',
            'password_confirmation' => 'required'
        ])) {
            return $this->back($_POST);
        }
        
        if ($password !== $passwordConfirmation) {
            $this->session->flash('error', 'Şifreler eşleşmiyor.');
            return $this->back(['token' => $token]);
        }
        
        $reset = $this->db->selectOne(
            "SELECT * FROM password_resets WHERE token = :token AND expires_at > NOW()",
            ['token' => $token]
        );
        
        if (!$reset) {
            $this->session->flash('error', 'Geçersiz veya süresi dolmuş şifre sıfırlama bağlantısı.');
            $this->redirect('/admin/login');
        }
        
        // Update password
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        $this->db->update('users', [
            'password' => $hashedPassword,
            'updated_at' => date('Y-m-d H:i:s')
        ], 'email = :email', ['email' => $reset['email']]);
        
        // Delete reset token
        $this->db->delete('password_resets', 'token = :token', ['token' => $token]);
        
        Logger::userAction('password_reset_completed', ['email' => $reset['email']]);
        
        $this->session->flash('success', 'Şifreniz başarıyla güncellendi. Giriş yapabilirsiniz.');
        $this->redirect('/admin/login');
    }
}
