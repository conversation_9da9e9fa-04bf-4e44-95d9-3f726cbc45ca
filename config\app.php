<?php
/**
 * Application Configuration
 * Otel Yönetim Sistemi
 */

return [
    'name' => $_ENV['APP_NAME'] ?? 'Otel Yönetim Sistemi',
    'version' => '1.0.0',
    'env' => $_ENV['APP_ENV'] ?? 'production',
    'debug' => $_ENV['APP_DEBUG'] ?? false,
    'url' => $_ENV['APP_URL'] ?? 'http://localhost',
    'timezone' => $_ENV['APP_TIMEZONE'] ?? 'Europe/Istanbul',
    'locale' => $_ENV['APP_LOCALE'] ?? 'tr',
    'fallback_locale' => 'en',
    
    'key' => $_ENV['APP_KEY'] ?? 'base64:' . base64_encode(random_bytes(32)),
    
    'cipher' => 'AES-256-CBC',
    
    'session' => [
        'lifetime' => 120, // minutes
        'expire_on_close' => false,
        'encrypt' => false,
        'files' => __DIR__ . '/../storage/sessions',
        'connection' => null,
        'table' => 'sessions',
        'store' => null,
        'lottery' => [2, 100],
        'cookie' => 'hotel_session',
        'path' => '/',
        'domain' => null,
        'secure' => false,
        'http_only' => true,
        'same_site' => 'lax',
    ],
    
    'security' => [
        'max_login_attempts' => 5,
        'lockout_duration' => 15, // minutes
        'password_min_length' => 8,
        'require_password_confirmation' => true,
        'csrf_token_lifetime' => 3600, // seconds
    ],
    
    'upload' => [
        'max_file_size' => 10 * 1024 * 1024, // 10MB
        'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
        'image_extensions' => ['jpg', 'jpeg', 'png', 'gif'],
        'paths' => [
            'slider' => '/storage/uploads/slider/',
            'gallery' => '/storage/uploads/gallery/',
            'banners' => '/storage/uploads/banners/',
            'documents' => '/storage/uploads/documents/',
        ],
    ],
    
    'pagination' => [
        'per_page' => 20,
        'max_per_page' => 100,
    ],
    
    'cache' => [
        'default' => 'file',
        'stores' => [
            'file' => [
                'driver' => 'file',
                'path' => __DIR__ . '/../storage/cache',
            ],
        ],
        'prefix' => 'hotel_system',
    ],
    
    'logging' => [
        'default' => 'daily',
        'channels' => [
            'daily' => [
                'driver' => 'daily',
                'path' => __DIR__ . '/../storage/logs/app.log',
                'level' => 'debug',
                'days' => 14,
            ],
            'security' => [
                'driver' => 'daily',
                'path' => __DIR__ . '/../storage/logs/security.log',
                'level' => 'info',
                'days' => 30,
            ],
        ],
    ],
    
    // Admin panel specific settings
    'admin' => [
        'prefix' => 'admin',
        'middleware' => ['auth', 'admin'],
        'layout' => 'layouts.admin',
    ],
    
    // Frontend specific settings
    'frontend' => [
        'layout' => 'layouts.frontend',
        'theme' => 'default',
    ],
];
